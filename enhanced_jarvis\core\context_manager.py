"""
Context Manager for Enhanced JARVIS
Manages conversation context, user state, and environmental awareness
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import json

@dataclass
class ContextEntry:
    """Single context entry"""
    key: str
    value: Any
    timestamp: datetime
    ttl: Optional[int] = None  # Time to live in seconds
    source: str = "system"
    confidence: float = 1.0

@dataclass
class UserContext:
    """User-specific context information"""
    user_id: str
    preferences: Dict[str, Any] = field(default_factory=dict)
    current_task: Optional[str] = None
    conversation_history: List[Dict[str, Any]] = field(default_factory=list)
    environmental_context: Dict[str, Any] = field(default_factory=dict)
    temporal_context: Dict[str, Any] = field(default_factory=dict)
    last_activity: datetime = field(default_factory=datetime.now)

class ContextManager:
    """
    Manages contextual information for intelligent conversation flow
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Context storage
        self.user_contexts: Dict[str, UserContext] = {}
        self.global_context: Dict[str, ContextEntry] = {}
        
        # Configuration
        self.max_history_length = config.get('max_history_length', 50)
        self.context_cleanup_interval = config.get('cleanup_interval', 300)  # 5 minutes
        
        # State
        self.is_initialized = False
        self._cleanup_task = None
    
    async def initialize(self):
        """Initialize the context manager"""
        try:
            self.logger.info("Initializing Context Manager...")
            
            # Start cleanup task
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
            
            self.is_initialized = True
            self.logger.info("Context Manager initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Context Manager: {e}")
            raise
    
    async def update_context(self, 
                           input_text: str, 
                           user_id: str, 
                           input_type: str = "text",
                           metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Update context with new user input
        
        Args:
            input_text: User's input
            user_id: User identifier
            input_type: Type of input
            metadata: Additional metadata
            
        Returns:
            Updated context dictionary
        """
        try:
            # Get or create user context
            if user_id not in self.user_contexts:
                self.user_contexts[user_id] = UserContext(user_id=user_id)
            
            user_context = self.user_contexts[user_id]
            
            # Update temporal context
            now = datetime.now()
            user_context.temporal_context.update({
                'current_time': now.isoformat(),
                'hour': now.hour,
                'day_of_week': now.weekday(),
                'is_weekend': now.weekday() >= 5,
                'time_since_last_activity': (now - user_context.last_activity).total_seconds()
            })
            
            # Update environmental context from metadata
            if metadata:
                user_context.environmental_context.update(metadata)
            
            # Add to conversation history
            conversation_entry = {
                'timestamp': now.isoformat(),
                'input': input_text,
                'input_type': input_type,
                'metadata': metadata or {}
            }
            
            user_context.conversation_history.append(conversation_entry)
            
            # Trim history if too long
            if len(user_context.conversation_history) > self.max_history_length:
                user_context.conversation_history = user_context.conversation_history[-self.max_history_length:]
            
            # Update last activity
            user_context.last_activity = now
            
            # Build comprehensive context
            context = {
                'user_id': user_id,
                'current_input': input_text,
                'input_type': input_type,
                'preferences': user_context.preferences,
                'current_task': user_context.current_task,
                'conversation_history': user_context.conversation_history[-5:],  # Last 5 exchanges
                'temporal_context': user_context.temporal_context,
                'environmental_context': user_context.environmental_context,
                'global_context': {k: v.value for k, v in self.global_context.items()},
                'session_length': len(user_context.conversation_history),
                'metadata': metadata or {}
            }
            
            self.logger.debug(f"Updated context for user {user_id}")
            return context
            
        except Exception as e:
            self.logger.error(f"Error updating context: {e}")
            return {'error': str(e)}
    
    async def set_user_preference(self, user_id: str, key: str, value: Any):
        """Set a user preference"""
        if user_id not in self.user_contexts:
            self.user_contexts[user_id] = UserContext(user_id=user_id)
        
        self.user_contexts[user_id].preferences[key] = value
        self.logger.info(f"Set preference {key} for user {user_id}")
    
    async def get_user_preference(self, user_id: str, key: str, default: Any = None) -> Any:
        """Get a user preference"""
        if user_id in self.user_contexts:
            return self.user_contexts[user_id].preferences.get(key, default)
        return default
    
    async def set_current_task(self, user_id: str, task: str):
        """Set the current task for a user"""
        if user_id not in self.user_contexts:
            self.user_contexts[user_id] = UserContext(user_id=user_id)
        
        self.user_contexts[user_id].current_task = task
        self.logger.info(f"Set current task '{task}' for user {user_id}")
    
    async def clear_current_task(self, user_id: str):
        """Clear the current task for a user"""
        if user_id in self.user_contexts:
            self.user_contexts[user_id].current_task = None
            self.logger.info(f"Cleared current task for user {user_id}")
    
    async def set_global_context(self, key: str, value: Any, ttl: Optional[int] = None, source: str = "system"):
        """Set global context that applies to all users"""
        self.global_context[key] = ContextEntry(
            key=key,
            value=value,
            timestamp=datetime.now(),
            ttl=ttl,
            source=source
        )
        self.logger.debug(f"Set global context: {key}")
    
    async def get_conversation_summary(self, user_id: str, last_n: int = 10) -> str:
        """Get a summary of recent conversation"""
        if user_id not in self.user_contexts:
            return "No conversation history available."
        
        history = self.user_contexts[user_id].conversation_history[-last_n:]
        if not history:
            return "No recent conversation history."
        
        summary_parts = []
        for entry in history:
            timestamp = datetime.fromisoformat(entry['timestamp']).strftime("%H:%M")
            summary_parts.append(f"[{timestamp}] {entry['input']}")
        
        return "\n".join(summary_parts)
    
    async def _periodic_cleanup(self):
        """Periodically clean up expired context entries"""
        while self.is_initialized:
            try:
                await asyncio.sleep(self.context_cleanup_interval)
                await self._cleanup_expired_context()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in context cleanup: {e}")
    
    async def _cleanup_expired_context(self):
        """Remove expired context entries"""
        now = datetime.now()
        expired_keys = []
        
        for key, entry in self.global_context.items():
            if entry.ttl and (now - entry.timestamp).total_seconds() > entry.ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.global_context[key]
            self.logger.debug(f"Removed expired context: {key}")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get context manager status"""
        return {
            'initialized': self.is_initialized,
            'active_users': len(self.user_contexts),
            'global_context_entries': len(self.global_context),
            'total_conversations': sum(len(ctx.conversation_history) for ctx in self.user_contexts.values())
        }
    
    async def shutdown(self):
        """Shutdown the context manager"""
        try:
            self.is_initialized = False
            
            if self._cleanup_task:
                self._cleanup_task.cancel()
                try:
                    await self._cleanup_task
                except asyncio.CancelledError:
                    pass
            
            self.logger.info("Context Manager shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during Context Manager shutdown: {e}")
