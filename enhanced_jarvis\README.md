# Enhanced JARVIS - Intelligent AI Assistant

A highly intelligent, context-aware, voice-enabled AI assistant that behaves like a digital co-pilot — capable of understanding natural language, automating complex tasks, retrieving and analyzing information, and interfacing with both the internet and personal/local systems.

## Features

### 🧠 Advanced AI Engine
- **Context-Aware Processing**: Maintains conversation context and user preferences
- **Intelligent Memory System**: Stores and retrieves relevant information from past interactions
- **Advanced Intent Recognition**: Uses multiple NLP approaches for accurate intent classification
- **Smart Decision Making**: Makes intelligent decisions based on context and safety policies
- **Natural Response Generation**: Generates contextual, personality-driven responses

### 🎤 Enhanced Voice Processing
- **Wake Word Detection**: Responds to "<PERSON>" or "<PERSON> <PERSON>"
- **Continuous Listening**: Always ready to help with background voice monitoring
- **Voice Activity Detection**: Distinguishes speech from background noise
- **High-Quality TTS**: Natural-sounding text-to-speech with emotion support
- **Multi-Language Support**: Configurable language settings

### 🤖 Task Automation
- **System Control**: Shutdown, restart, open/close applications
- **Web Integration**: Search Google, YouTube, browse websites
- **Media Control**: Play, pause, stop music and media
- **Communication**: Send messages, make calls (with integrations)
- **Information Retrieval**: Get time, date, weather, news
- **Memory Management**: Remember and recall information

### 🔧 Modular Architecture
- **Plugin System**: Easily extensible with new capabilities
- **Safety Policies**: Built-in safety checks for sensitive operations
- **Configuration**: Highly configurable through JSON/YAML files
- **Async Processing**: Non-blocking, concurrent task execution
- **Error Handling**: Robust error handling and recovery

## Installation

### Prerequisites
- Python 3.8 or higher
- Microphone for voice input
- Speakers for audio output
- Internet connection for some features

### Quick Install
```bash
# Clone the repository
git clone https://github.com/yourusername/enhanced-jarvis.git
cd enhanced-jarvis

# Install dependencies
pip install -r requirements.txt

# Download spaCy language model
python -m spacy download en_core_web_sm

# Run Enhanced JARVIS
python -m enhanced_jarvis.main
```

### Development Install
```bash
# Install in development mode
pip install -e .

# Install development dependencies
pip install -e .[dev]

# Run tests
pytest
```

## Usage

### Voice Mode (Default)
```bash
python -m enhanced_jarvis.main
```

### Text Mode (Testing)
```bash
python -m enhanced_jarvis.main --text-mode
```

### With Custom Configuration
```bash
python -m enhanced_jarvis.main --config config/my_config.json
```

## Voice Commands

### Basic Interaction
- "Hello Jarvis" - Greet JARVIS
- "How are you?" - Check JARVIS status
- "What can you do?" - Get help information
- "Goodbye" - End session

### System Control
- "Shutdown the system" - Shutdown computer
- "Restart the computer" - Restart system
- "Open Chrome" - Open applications
- "Close Notepad" - Close applications

### Information Queries
- "What time is it?" - Get current time
- "What's the date?" - Get current date
- "What's the weather?" - Get weather information
- "Tell me the news" - Get latest news

### Web and Search
- "Search for Python tutorials" - Web search
- "YouTube search funny videos" - YouTube search
- "Google machine learning" - Google search

### Memory and Notes
- "Remember to buy milk" - Store information
- "What do you remember?" - Recall stored information
- "Note that meeting is at 3pm" - Take notes

### Media Control
- "Play music" - Start music playback
- "Stop music" - Stop playback
- "Play some jazz" - Play specific music

## Configuration

### Basic Configuration
Create a `config.json` file:

```json
{
  "speech_recognition": {
    "language": "en-US",
    "wake_words": ["jarvis", "hey jarvis"],
    "continuous_listening": true
  },
  "text_to_speech": {
    "engine": "pyttsx3",
    "voice_profiles": {
      "default": {
        "rate": 150,
        "volume": 0.9
      }
    }
  },
  "ai_engine": {
    "memory": {
      "max_memories_per_user": 10000
    }
  }
}
```

### Voice Profiles
Customize voice characteristics:

```json
{
  "text_to_speech": {
    "voice_profiles": {
      "friendly": {
        "rate": 160,
        "volume": 0.9,
        "emotion_modifiers": {
          "happy": {"rate": 170, "pitch": 10},
          "calm": {"rate": 140, "pitch": -5}
        }
      }
    }
  }
}
```

## Architecture

### Core Components

1. **AI Engine** (`core/ai_engine.py`)
   - Orchestrates all AI components
   - Manages processing pipeline
   - Handles context and memory

2. **Context Manager** (`core/context_manager.py`)
   - Maintains conversation context
   - Tracks user preferences
   - Manages temporal and environmental context

3. **Memory System** (`core/memory_system.py`)
   - Stores long-term and short-term memories
   - Retrieves relevant information
   - Learns from interactions

4. **Intent Recognition** (`core/intent_recognition.py`)
   - Classifies user intents
   - Extracts entities from text
   - Supports multiple recognition methods

5. **Decision Engine** (`core/decision_engine.py`)
   - Makes intelligent decisions
   - Applies safety policies
   - Plans action sequences

6. **Response Generator** (`core/response_generator.py`)
   - Generates natural responses
   - Applies personality traits
   - Handles emotional context

7. **Voice Processing** (`voice/`)
   - Speech recognition with wake word detection
   - Text-to-speech with emotion support
   - Audio processing and filtering

8. **Task Executor** (`automation/task_executor.py`)
   - Executes system commands
   - Handles automation tasks
   - Manages concurrent operations

## Extending JARVIS

### Adding New Intents
1. Add intent patterns to `intent_recognition.py`
2. Add action mappings to `decision_engine.py`
3. Add response templates to `response_generator.py`
4. Implement task handlers in `task_executor.py`

### Creating Plugins
```python
# plugins/my_plugin.py
class MyPlugin:
    def __init__(self, config):
        self.config = config
    
    async def handle_my_action(self, parameters):
        # Your custom logic here
        return TaskResult(success=True, message="Action completed")
```

### Custom Voice Commands
Add to intent patterns:
```python
'my_custom_intent': IntentPattern(
    intent='my_custom_intent',
    patterns=[r'\bmy custom command\b'],
    entities=['parameter'],
    examples=["my custom command with parameter"]
)
```

## Safety and Privacy

### Built-in Safety Features
- Confirmation required for destructive actions
- Time-based restrictions for sensitive operations
- Rate limiting for communication features
- Privacy mode for sensitive environments

### Data Privacy
- All data stored locally by default
- No cloud dependencies for core functionality
- User control over data retention
- Encrypted storage options available

## Troubleshooting

### Common Issues

**Microphone not working:**
```bash
# Test microphone access
python -c "import speech_recognition as sr; print(sr.Microphone.list_microphone_names())"
```

**TTS not working:**
```bash
# Test TTS engine
python -c "import pyttsx3; engine = pyttsx3.init(); engine.say('test'); engine.runAndWait()"
```

**Dependencies issues:**
```bash
# Reinstall dependencies
pip install --force-reinstall -r requirements.txt
```

### Debug Mode
```bash
python -m enhanced_jarvis.main --debug
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Built upon the foundation of the original JARVIS project
- Inspired by modern AI assistants and voice interfaces
- Uses various open-source libraries and frameworks

## Roadmap

### Upcoming Features
- [ ] Web-based dashboard interface
- [ ] Mobile app integration
- [ ] Smart home device control
- [ ] Advanced learning capabilities
- [ ] Multi-user support
- [ ] Cloud synchronization options
- [ ] Plugin marketplace
- [ ] Voice training and customization

### Advanced AI Features
- [ ] Transformer-based NLP models
- [ ] Computer vision integration
- [ ] Predictive task automation
- [ ] Emotional intelligence
- [ ] Contextual learning
- [ ] Multi-modal interaction

---

**Enhanced JARVIS** - Your intelligent digital co-pilot, ready to assist with any task!
