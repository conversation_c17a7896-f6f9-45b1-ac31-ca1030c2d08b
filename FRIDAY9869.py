from Features import GoogleSearch
from bs4 import BeautifulSoup
import os.path
import speech_recognition as sr
from requests import get
import pyautogui
import pygame
import datetime
import time
import sounddevice as sd
from keyboard import write
from time import sleep
import pyttsx3
import requests
import os
import random
from win10toast import ToastNotifier
import pywhatkit
from keyboard import press
from keyboard import press_and_release
import keyboard
import pyautogui
from pyautogui import click
from time import sleep


engine = pyttsx3.init('sapi5')
voices = engine.getProperty('voices')
# print(voices)
engine.setProperty('voice', voices[9].id)
engine.setProperty('rate',145)


def speak(audio):
    print(" ")
    print(f": {audio}")
    engine.say(audio)
    engine.runAndWait()
    print(" ")


def takecommand():

    r = sr.Recognizer()

    with sr.Microphone() as source:

        print(": Listening....")

        r.pause_threshold = 1

        audio = r.listen(source,2,4)

    try:

        print(": Recognizing...")

        query = r.recognize_google(audio,language='en-in')

        print(f": Your Command : {query}\n")

    except:
        return ""

    return query.lower()


def wish():
    hour = int(datetime.datetime.now().hour)
    tt = time.strftime("%I:%M%p")
    day = datetime.datetime.today().weekday()+1
    Day_dict = {1:"Monday",2:"Tuseday",3:"Wednesday",4:"Thursday",5:"Friday",6:"Saturday",7:"Sunday"}

    if day in Day_dict.keys():
        day_of_the_week = Day_dict[day]

    if hour>=0 and hour<=12:
        speak(f"Good Morning,It's {tt},Today Is {day_of_the_week}")

    elif hour>=12 and hour<=18:
        speak(f"Good Afternoon,It's {tt},Today Is {day_of_the_week}")

    else:
        speak(f"Good Evening,It's {tt},Today Is {day_of_the_week}")

    # speak("I Am Friday , Your Virtual Assistent , Please Tell Me , How Can I Help You")


def DAYS():
    day = datetime.datetime.today().weekday() + 1
    Day_dict = {1: 'Monday', 2: 'Tuesday', 3: 'Wednesday',
                4: 'Thursday', 5: 'Friday', 6: 'Saturday',
                7: 'Sunday'}
    if day in Day_dict.keys():
        day_of_the_week = Day_dict[day]
        print(day_of_the_week)
        speak("The day is " + day_of_the_week)


def sleeptime():
    if os.path.exists("Goodnight.txt"):
        starttime = int(datetime.datetime.now().minute)
        f = open("Goodnight.txt", "r+")
        endtime = f.readlines()
        sleeptime = starttime - int(endtime[0])
        if sleeptime < 1:
            speak("You Did not sleep at all Jay")
        else:
            speak(f"You slept for {sleeptime} hours")


def news():
    main_url = "http://newsapi.org/v2/top-headlines?country=in&apiKey=********************************"
    main_page = requests.get(main_url).json()
    # print(main_page)
    articles = main_page["articles"]
    # print(articles)
    head = []
    day = ["first", "second", "third", "fourth", "fifth", "sixth", "seventh", "eighth", "ninth", "tenth"]
    for ar in articles:
        head.append(ar["title"])
    for i in range(len(day)):
        # print(f"today's {day[i]} news is: ", head[i])
        speak(f"today's {day[i]} news is : {head[i]}")


def TurnOff():
    speak("Hold On a Sec ! Your system is on its way to shut down")
    speak("okay , By , Jay")
    os.system("shutdown /s /t 10")


def ScreenShot():
    speak('okay Jay,What Should I Name That File ?')
    path = takecommand()
    path1name = path + '.png'
    path1 = 'E:\\Jarvis\\JARVIS.9869\\DataBase\\ScreenShot\\' + path1name
    kk = pyautogui.screenshot()
    kk.save(path1)
    os.startfile('E:\\Jarvis\\JARVIS.9869\\DataBase\\ScreenShot')
    speak('Here Is Your ScreenShot')


def CountDown():
    # speak('Please enter How many second countdown')
    # sec = int(input('How many sec countdown?'))

    speak('tell me how many second countdown')
    sec = int(takecommand())

    for x in range(sec):
        speak(str(sec - x) + '')
        time.sleep(1)


def CPU():
    from pynotifier import Notification
    import psutil
    battery = psutil.sensors_battery()
    speak(f"battery is at")
    speak(battery.percent)
    battery = psutil.sensors_battery()
    percent = battery.percent
    Notification("Battery Percentage", str(percent) + "%Percent Remaining", duration=10).send()


def ScroolDown():
    for i in range(2):
        pyautogui.scroll(-500)


def ScroolUp():
    for i in range(2):
        pyautogui.scroll(+500)


def StopW():
    speak("For how many minutes?")
    timing = takecommand()
    timing = timing.replace('minutes', '')
    timing = timing.replace('minute', '')
    timing = timing.replace('for', '')
    timing = float(timing)
    timing = timing * 60
    speak(f'I will remind you in {timing} seconds')
    time.sleep(timing)
    speak('Your time has been finished Jay')


def SwitchWindo():
    pyautogui.keyDown("alt")
    pyautogui.press("tab")
    time.sleep(1)
    pyautogui.keyUp("alt")


def Recsound():
    fs = 44100
    speak("what should be the length of your sound wave Plz answer in seconds")
    ans = int(takecommand())
    seconds = ans
    recorded = sd.rec(int(seconds * fs), samplerate=fs, channels=2)
    sd.wait()
    speak("sucessfully recoreded")
    speak("what should i keep the file name")
    filename = takecommand()
    write(filename + '.mp3', fs, recorded)
    speak("sucessfuly saved")
    try:
        speak("should i show you")
        reply = takecommand()
        if "yes" in reply:
            os.startfile(filename + ".mp3")
    except:
        if "no" in reply:
            speak("okay next command sir")


def Opener(Name):
    sleep(0.5)
    pyautogui.press('win')
    sleep(0.5)
    keyboard.write(Name)
    sleep(0.5)
    keyboard.press('enter')
    sleep(0.5)


def startup():
    speak("Initializing Friday")
    speak("Starting all systems applications")
    speak("Installing and checking all drivers")
    speak("Caliberating and examining all the core processors")
    speak("Checking the internet connection")
    speak("Wait a moment sir")
    speak("All drivers are up and running")
    speak("All systems have been activated")
    speak('Now I Am Ready At Online')


def SwitchWindo():
    pyautogui.keyDown("alt")
    pyautogui.press("tab")
    time.sleep(1)
    pyautogui.keyUp("alt")


toast = ToastNotifier()
toast.show_toast(" Friday ", "The Friday Is Now Activated", duration=3)

def TaskExecotion():
    # wish()
    while True:
        query = takecommand()

########################################/ Hello,Bye \###################################################################

        if "hello" in query:
            from DataBase.ChatBot.ChatBot import ChatterBot
            reply = ChatterBot(query)
            speak(reply)

        elif "thanks" in query or "thank you" in query:
            from DataBase.ChatBot.ChatBot import ChatterBot
            reply = ChatterBot(query)
            speak(reply)

        elif "by" in query or "you can sleep now" in query:
            from DataBase.ChatBot.ChatBot import ChatterBot
            reply = ChatterBot(query)
            speak(reply)
            break

########################################/ Time,Date,Day,Morngin,Night Program \#########################################

        elif "what is the time" in query:
            strTime = datetime.datetime.now().strftime("%I:%M:%p")
            speak(f"the time is {strTime}")

        elif "what is today's date" in query or 'date' in query:
            date = datetime.datetime.now().strftime("%A:%d:%B:%Y")
            speak(f"Today is {date} ")

        elif "what day is it" in query or 'today' in query:
            speak('')
            DAYS()

        elif "good morning" in query:
            strTime = datetime.datetime.now().strftime("%I:%M:%p")
            search = "temperature in Malegaon"
            url = f"https://www.google.com/search?q={search}"
            r = requests.get(url)
            data = BeautifulSoup(r.text, "html.parser")
            temperature = data.find("div", class_="BNeawe").text
            speak(f"Jay it's {strTime},The temperature Is {temperature}, And Today Outside Weather Will Be sunny")

        elif "good night" in query:
            strTime = datetime.datetime.now().strftime("%I:%M:%p").replace(":", " ")
            gtime = strTime.replace(":", " ")
            speak(f"Good night  it is {gtime} sleep tight..")
            break

########################################/ Google,Youtube,Alarm Programs \###############################################

        elif "google search" in query:
            speak('')
            GoogleSearch(query)

        elif 'youtube search' in query:
            Query = query.replace("Friday","")
            query = Query.replace("youtube search","")
            from Features import YouTubeSearch
            YouTubeSearch(query)

        elif 'set alarm' in query:
            from Features import Alarm
            Alarm(query)

        elif 'stop alarm' in query or 'can you please shut down this alarm' in query:
            pygame.mixer.music.stop()
            sleep(1)
            speak('Good Morning Jay')

########################################/ Just Download Youtube Video \#################################################

        elif 'download' in query:
            from Features import DownloadYouTube
            DownloadYouTube()

########################################/ Whatsapp(Message,Call,Video),Note,TimeTable,Screenshot,TurnOff \##############

        elif 'whatsapp message' in query:
            name = query.replace("whatsapp message", "")
            name = name.replace("send ", "")
            name = name.replace("to ", "")
            Name = str(name)
            speak(f"Whats The Message For {Name}")
            MSG = takecommand()
            from Automations import WhatsappMsg
            WhatsappMsg(Name, MSG)

        elif 'whatsapp call' in query:
            from Automations import WhatsappCall
            name = query.replace("whatsapp call ", "")
            name = name.replace("Friday ", "")
            Name = str(name)
            WhatsappCall(Name)

        elif 'video call' in query:
            from Automations import WhatsappVideo
            name = query.replace("video call ", "")
            name = name.replace("Friday ", "")
            Name = str(name)
            WhatsappVideo(Name)

        elif 'write a note' in query:
            from Automations import Notepad
            Notepad()

        elif 'close notepad' in query:
            from Automations import CloseNotepad
            CloseNotepad()

        elif 'time table' in query:
            from Automations import TimeTable
            TimeTable()

        elif 'screenshot' in query:
            ScreenShot()
            speak("Done")

        elif 'Turn off' in query or 'power off' in query or 'shutdown' in query:
            TurnOff()
            speak("")

########################################/ Remember,Repeat(Words),Record(voice),Countdown,Cpu \##########################

        elif 'remember that' in query:
            speak("what should i remember Jay")
            rememberMessage = takecommand()
            speak("you said me to remember" + rememberMessage)
            remember = open('data.txt', 'w')
            remember.write(rememberMessage)
            remember.close()

        elif 'do you remember anything' in query or 'anything' in query or 'remember anything' in query:
            remember = open('data.txt', 'r')
            speak("you said me to remember that" + remember.read())

        elif 'repeat my word' in query:
            speak("speak Jay")
            jj = takecommand()
            speak(f"you said : {jj}")

        elif 'record my voice' in query:
            speak('okay jay')
            Recsound()

        elif 'countdown' in query:
            CountDown()
            speak('Your countdown is complete')

        elif 'cpu' in query or 'battery percentage' in query:
            speak('')
            CPU()

#######################################/ Scroll(Down),Scroll(Up),Switch(Windows),Stopwatch \############################

        elif 'scroll down' in query or 'go down' in query:
            speak('')
            ScroolDown()

        elif 'scroll up' in query or 'go up' in query:
            speak('')
            ScroolUp()

        elif 'switch the window' in query:
            speak('')
            SwitchWindo()

        elif 'stopwatch' in query:
            speak('')
            StopW()

########################################/ Ip(Address),News \############################################################

        elif "tell me ip address" in query or 'ip address' in query:
            ip = get("https://api.ipify.org").text
            speak(f"your ip address is {ip}")

        elif "tell me news" in query or "give me today's update" in query:
            speak("please wait Jay, feteching the leatest news")
            news()
            speak("that's is today's news")

########################################/ How Are You(Fine) \###########################################################

        elif 'how are you' in query:
            stMsgs = ['Just doing my thing!', 'I am fine!', 'Nice!', 'I am nice and full of energy',
                      'i am okey']
            ans_q = random.choice(stMsgs)
            speak(ans_q)
            speak('How Are You Jay')
            query = takecommand()
            if 'fine' in query or 'happy' in query or 'good' in query:
                stMsgs1 = ["O , That's Good"]
                speak(stMsgs1)
            elif 'sad' in query or "not okay" in query or 'upset' in query:
                speak("What happened Jay?")
                query = takecommand()
                if 'nothing much' in query or 'leave it' in query or "don't want to discuss" in query:
                    speak("Everything will be okay!")
                    speak("Let me make you happier !")
                    speak("You may wish to listen to some songs!")
                else:
                    speak('')

        elif 'fine' in query or 'happy' in query or 'good' in query:
            stMsgs1 = ["O , That's Good"]
            speak(stMsgs1)
########################################/ Play(Song),Stop(Music),Play(Next),Sound(Up,Down) \############################

        elif 'play some' in query:
            pywhatkit.playonyt(query)
            speak('I thing I know what to play')

        elif "play music" in query:
            click(x=18, y=747)
            sleep(0.5)
            write("Resso")
            sleep(0.5)
            press("Enter")
            sleep(15)
            click(x=186, y=482)
            sleep(5)
            click(x=394, y=292)
            sleep(4)
            click(x=599, y=627)

        elif "stop music" in query:
            press("Space")

        elif 'play next' in query or 'next' in query:
            press_and_release("Alt + Right")

        elif 'sound' in query:
            press_and_release('Alt + up')

        elif 'volume down' in query:
            press_and_release("Alt + down")

###############################/ Colse(All),Open(AnyThink) \############################################################

        elif 'close it' in query:
            speak("Okay , Jay")
            press_and_release('alt + f4')

        elif "open" in query:
            Name = query.replace('open', "")
            Name = Name.replace('start', "")
            Name = Name.replace(' ', '')
            Opener(Name=Name)

###############################/ Introduce(Friday) \####################################################################

        elif "who are you" in query:
            speak("I Am Friday , An Artificial Personal Assistance. Always At Your Service")
            speak("I Was Built By Mr. Jayesh Patil, To Make Your Things Easy")
            speak("Just Command Me What Are Your Pending Works ")
            speak("I Will Be Happy To Do Those Works According The Your Commands")
            speak("Thank You")

        elif "what's your name" in query:
            speak('My Name Is Friday')

        elif "don't touch me" in query:
            speak(f"{query} You don't have permission to use me")
            speak('')

        elif 'check all the systems' in query:
            speak('')
            startup()

        elif 'what we are working' in query:
            remember = open('data.txt', 'r')
            speak("yesterday we are working" + remember.read())

        elif 'what are you doing' in query:
            stMsgs = ['Is There Any Work For Me', 'nothing', 'any other work from me', 'doing my think']
            ans_q = random.choice(stMsgs)
            speak(ans_q)

        elif 'who created you' in query or 'who make you' in query:
            speak("I Have Been Created By Jay,")
            speak("")

        elif 'who are you' in query:
            speak("I am Friday virtual assistant created")
            speak("by jayesh")

        elif 'oh Friday' in query or 'oh no' in query or 'oh my God' in query:
            speak("what's happend,jayesh")
            speak("May I help you")

###############################/ Volume(Control) \######################################################################

        elif 'volume mute' in query or 'mute' in query:
            speak('')
            pyautogui.press("volumemute")

        elif 'volume 10%' in query:
            click(x=1188, y=749)
            sleep(2)
            click(x=1091, y=696)
            sleep(2)
            click(x=1188, y=749)

        elif 'volume 20%' in query:
            click(x=1188, y=749)
            sleep(2)
            click(x=1114, y=698)
            sleep(2)
            click(x=1188, y=749)

        elif 'volume 30%' in query:
            click(x=1188, y=749)
            sleep(2)
            click(x=1136, y=700)
            sleep(2)
            click(x=1188, y=749)

        elif 'volume 40%' in query:
            click(x=1188, y=749)
            sleep(2)
            click(x=1158, y=699)
            sleep(2)
            click(x=1188, y=749)

        elif 'volume 50%' in query:
            click(x=1188, y=749)
            sleep(2)
            click(x=1179, y=698)
            sleep(2)
            click(x=1188, y=749)

        elif 'volume 60%' in query:
            click(x=1188, y=749)
            sleep(2)
            click(x=1202, y=698)
            sleep(2)
            click(x=1188, y=749)

        elif 'volume 70%' in query:
            click(x=1188, y=749)
            sleep(2)
            click(x=1225, y=697)
            sleep(2)
            click(x=1188, y=749)

        elif 'volume 80%' in query:
            click(x=1188, y=749)
            sleep(2)
            click(x=1247, y=699)
            sleep(2)
            click(x=1188, y=749)

        elif 'volume 90%' in query:
            click(x=1188, y=749)
            sleep(2)
            click(x=1271, y=699)
            sleep(2)
            click(x=1188, y=749)

        elif 'volume full' in query or 'volume 100%' in query:
            click(x=1188, y=749)
            sleep(2)
            click(x=1293, y=696)
            sleep(2)
            click(x=1188, y=749)

###############################/ Switch(Tab) \######################################################################

        elif 'switch the window' in query:
            speak('')
            SwitchWindo()

        elif 'sketch' in query:
            os.startfile('C:\\Users\\<USER>\\Pictures\\Video Projects\\')

#<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<

stMsgs = ['Hello Jay .',
          "Hey , What's Up ?",
          "Hey How Are You ?",
          "Hello Jay , Nice To Meet You Again .",
          "Of Course Jay , Hello ."]
ans_q = random.choice(stMsgs)
speak(ans_q)
TaskExecotion()

#>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
