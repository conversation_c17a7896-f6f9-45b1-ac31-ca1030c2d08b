"""
Enhanced Speech Recognition for JARVIS
Advanced voice processing with wake word detection and noise filtering
"""

import asyncio
import logging
import numpy as np
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from datetime import datetime
import threading
import queue
import time

# Audio processing libraries
import speech_recognition as sr
import pyaudio
import webrtcvad
import collections
import wave

@dataclass
class VoiceCommand:
    """Recognized voice command"""
    text: str
    confidence: float
    timestamp: datetime
    audio_duration: float
    language: str
    metadata: Dict[str, Any]

@dataclass
class AudioFrame:
    """Audio frame for processing"""
    data: bytes
    timestamp: float
    sample_rate: int
    channels: int

class WakeWordDetector:
    """Simple wake word detection using keyword matching"""
    
    def __init__(self, wake_words: List[str], sensitivity: float = 0.7):
        self.wake_words = [word.lower() for word in wake_words]
        self.sensitivity = sensitivity
        self.logger = logging.getLogger(__name__)
    
    def detect(self, text: str) -> bool:
        """Detect if wake word is present in text"""
        text_lower = text.lower()
        for wake_word in self.wake_words:
            if wake_word in text_lower:
                return True
        return False

class VoiceActivityDetector:
    """Voice Activity Detection using WebRTC VAD"""
    
    def __init__(self, sample_rate: int = 16000, frame_duration: int = 30):
        self.sample_rate = sample_rate
        self.frame_duration = frame_duration
        self.frame_size = int(sample_rate * frame_duration / 1000)
        
        # WebRTC VAD (aggressiveness: 0-3, higher = more aggressive)
        self.vad = webrtcvad.Vad(2)
        
        # Ring buffer for smoothing
        self.ring_buffer_size = 10
        self.ring_buffer = collections.deque(maxlen=self.ring_buffer_size)
        
        self.logger = logging.getLogger(__name__)
    
    def is_speech(self, audio_frame: bytes) -> bool:
        """Determine if audio frame contains speech"""
        try:
            # Ensure frame is correct size
            if len(audio_frame) != self.frame_size * 2:  # 2 bytes per sample for 16-bit
                return False
            
            # Use WebRTC VAD
            is_speech = self.vad.is_speech(audio_frame, self.sample_rate)
            
            # Add to ring buffer for smoothing
            self.ring_buffer.append(is_speech)
            
            # Return True if majority of recent frames contain speech
            speech_count = sum(self.ring_buffer)
            return speech_count > len(self.ring_buffer) // 2
            
        except Exception as e:
            self.logger.error(f"Error in VAD: {e}")
            return False

class EnhancedSpeechRecognizer:
    """
    Enhanced speech recognition with advanced features
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Audio configuration
        self.sample_rate = config.get('sample_rate', 16000)
        self.chunk_size = config.get('chunk_size', 1024)
        self.channels = config.get('channels', 1)
        self.format = pyaudio.paInt16
        
        # Recognition configuration
        self.language = config.get('language', 'en-US')
        self.wake_words = config.get('wake_words', ['jarvis', 'hey jarvis'])
        self.continuous_listening = config.get('continuous_listening', True)
        self.noise_threshold = config.get('noise_threshold', 300)
        
        # Components
        self.recognizer = sr.Recognizer()
        self.microphone = None
        self.wake_word_detector = WakeWordDetector(self.wake_words)
        self.vad = VoiceActivityDetector(self.sample_rate)
        
        # Audio stream
        self.audio = None
        self.stream = None
        
        # Threading
        self.listening = False
        self.audio_queue = queue.Queue()
        self.command_callbacks: List[Callable] = []
        
        # State
        self.is_initialized = False
        self.wake_word_detected = False
        self.last_activity_time = time.time()
    
    async def initialize(self):
        """Initialize the speech recognizer"""
        try:
            self.logger.info("Initializing Enhanced Speech Recognizer...")
            
            # Initialize PyAudio
            self.audio = pyaudio.PyAudio()
            
            # Initialize microphone
            self.microphone = sr.Microphone(sample_rate=self.sample_rate)
            
            # Calibrate for ambient noise
            with self.microphone as source:
                self.logger.info("Calibrating for ambient noise...")
                self.recognizer.adjust_for_ambient_noise(source, duration=2)
                self.logger.info(f"Noise threshold set to: {self.recognizer.energy_threshold}")
            
            # Configure recognizer
            self.recognizer.energy_threshold = max(self.noise_threshold, self.recognizer.energy_threshold)
            self.recognizer.dynamic_energy_threshold = True
            self.recognizer.pause_threshold = 0.8
            self.recognizer.phrase_threshold = 0.3
            
            self.is_initialized = True
            self.logger.info("Speech Recognizer initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Speech Recognizer: {e}")
            raise
    
    async def start_listening(self):
        """Start continuous listening for voice commands"""
        if not self.is_initialized:
            await self.initialize()
        
        try:
            self.listening = True
            
            if self.continuous_listening:
                # Start continuous listening in background thread
                listen_thread = threading.Thread(target=self._continuous_listen_thread)
                listen_thread.daemon = True
                listen_thread.start()
                
                self.logger.info("Started continuous listening")
            else:
                self.logger.info("Speech recognizer ready for manual listening")
                
        except Exception as e:
            self.logger.error(f"Error starting listening: {e}")
            raise
    
    def _continuous_listen_thread(self):
        """Background thread for continuous listening"""
        while self.listening:
            try:
                # Listen for audio
                with self.microphone as source:
                    # Listen for wake word or direct command
                    audio = self.recognizer.listen(
                        source, 
                        timeout=1, 
                        phrase_time_limit=5
                    )
                
                # Process audio in separate thread to avoid blocking
                process_thread = threading.Thread(
                    target=self._process_audio_async, 
                    args=(audio,)
                )
                process_thread.daemon = True
                process_thread.start()
                
            except sr.WaitTimeoutError:
                # Timeout is normal in continuous listening
                continue
            except Exception as e:
                self.logger.error(f"Error in continuous listening: {e}")
                time.sleep(1)  # Brief pause before retrying
    
    def _process_audio_async(self, audio):
        """Process audio in background thread"""
        try:
            # Convert to text
            text = self.recognizer.recognize_google(audio, language=self.language)
            
            if text:
                self.logger.debug(f"Recognized: {text}")
                
                # Check for wake word if not already detected
                if not self.wake_word_detected:
                    if self.wake_word_detector.detect(text):
                        self.wake_word_detected = True
                        self.last_activity_time = time.time()
                        self.logger.info("Wake word detected!")
                        
                        # Remove wake word from text
                        cleaned_text = self._remove_wake_words(text)
                        if cleaned_text.strip():
                            # Process command immediately
                            self._handle_command(cleaned_text, audio)
                        return
                else:
                    # Wake word already detected, process as command
                    self._handle_command(text, audio)
                    
                    # Reset wake word detection after processing
                    self.wake_word_detected = False
            
        except sr.UnknownValueError:
            # Could not understand audio - this is normal
            pass
        except sr.RequestError as e:
            self.logger.error(f"Speech recognition service error: {e}")
        except Exception as e:
            self.logger.error(f"Error processing audio: {e}")
    
    def _remove_wake_words(self, text: str) -> str:
        """Remove wake words from recognized text"""
        text_lower = text.lower()
        for wake_word in self.wake_words:
            text_lower = text_lower.replace(wake_word.lower(), "")
        return text_lower.strip()
    
    def _handle_command(self, text: str, audio):
        """Handle recognized voice command"""
        try:
            # Create voice command object
            command = VoiceCommand(
                text=text.strip(),
                confidence=0.8,  # Would be actual confidence from recognizer
                timestamp=datetime.now(),
                audio_duration=len(audio.frame_data) / (self.sample_rate * 2),  # Approximate
                language=self.language,
                metadata={
                    'wake_word_used': self.wake_word_detected,
                    'audio_length': len(audio.frame_data)
                }
            )
            
            # Notify all registered callbacks
            for callback in self.command_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        # Schedule async callback
                        asyncio.create_task(callback(command))
                    else:
                        # Call sync callback
                        callback(command)
                except Exception as e:
                    self.logger.error(f"Error in command callback: {e}")
            
            self.logger.info(f"Processed voice command: {text}")
            
        except Exception as e:
            self.logger.error(f"Error handling command: {e}")
    
    async def listen_once(self, timeout: float = 5.0) -> Optional[VoiceCommand]:
        """Listen for a single voice command"""
        if not self.is_initialized:
            await self.initialize()
        
        try:
            with self.microphone as source:
                self.logger.debug("Listening for single command...")
                audio = self.recognizer.listen(source, timeout=timeout, phrase_time_limit=10)
            
            # Recognize speech
            text = self.recognizer.recognize_google(audio, language=self.language)
            
            if text:
                command = VoiceCommand(
                    text=text.strip(),
                    confidence=0.8,
                    timestamp=datetime.now(),
                    audio_duration=len(audio.frame_data) / (self.sample_rate * 2),
                    language=self.language,
                    metadata={'single_listen': True}
                )
                
                self.logger.info(f"Single listen result: {text}")
                return command
            
        except sr.WaitTimeoutError:
            self.logger.debug("Listen timeout - no speech detected")
        except sr.UnknownValueError:
            self.logger.debug("Could not understand audio")
        except sr.RequestError as e:
            self.logger.error(f"Speech recognition service error: {e}")
        except Exception as e:
            self.logger.error(f"Error in single listen: {e}")
        
        return None
    
    def add_command_callback(self, callback: Callable):
        """Add callback for voice commands"""
        self.command_callbacks.append(callback)
        self.logger.debug("Added command callback")
    
    def remove_command_callback(self, callback: Callable):
        """Remove command callback"""
        if callback in self.command_callbacks:
            self.command_callbacks.remove(callback)
            self.logger.debug("Removed command callback")
    
    async def stop_listening(self):
        """Stop continuous listening"""
        self.listening = False
        self.wake_word_detected = False
        self.logger.info("Stopped listening")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get speech recognizer status"""
        return {
            'initialized': self.is_initialized,
            'listening': self.listening,
            'wake_word_detected': self.wake_word_detected,
            'continuous_listening': self.continuous_listening,
            'language': self.language,
            'wake_words': self.wake_words,
            'energy_threshold': getattr(self.recognizer, 'energy_threshold', 0),
            'callbacks_registered': len(self.command_callbacks)
        }
    
    async def shutdown(self):
        """Shutdown the speech recognizer"""
        try:
            await self.stop_listening()
            
            if self.stream:
                self.stream.stop_stream()
                self.stream.close()
            
            if self.audio:
                self.audio.terminate()
            
            self.is_initialized = False
            self.logger.info("Speech Recognizer shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during Speech Recognizer shutdown: {e}")
