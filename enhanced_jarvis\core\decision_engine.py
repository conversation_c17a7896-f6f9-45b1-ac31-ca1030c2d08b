"""
Decision Engine for Enhanced JARVIS
Makes intelligent decisions about actions to take based on intent and context
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import json

@dataclass
class Action:
    """Individual action to be executed"""
    action_type: str
    parameters: Dict[str, Any]
    priority: int = 1
    requires_confirmation: bool = False
    estimated_duration: float = 0.0

@dataclass
class Decision:
    """Decision result with actions to execute"""
    actions: List[Action]
    confidence: float
    reasoning: str
    requires_user_confirmation: bool = False
    metadata: Dict[str, Any] = None

class DecisionEngine:
    """
    Intelligent decision making engine for JARVIS
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Decision rules and policies
        self.action_mappings = self._load_action_mappings()
        self.safety_policies = self._load_safety_policies()
        self.user_preferences = {}
        
        # State
        self.is_initialized = False
    
    async def initialize(self):
        """Initialize the decision engine"""
        try:
            self.logger.info("Initializing Decision Engine...")
            
            # Load any additional configuration
            await self._load_user_policies()
            
            self.is_initialized = True
            self.logger.info("Decision Engine initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Decision Engine: {e}")
            raise
    
    def _load_action_mappings(self) -> Dict[str, Any]:
        """Load intent to action mappings"""
        return {
            # Greeting and conversation
            'greeting': {
                'actions': [
                    {'type': 'speak', 'template': 'greeting_response'}
                ],
                'confidence_threshold': 0.3
            },
            
            'goodbye': {
                'actions': [
                    {'type': 'speak', 'template': 'goodbye_response'},
                    {'type': 'system', 'action': 'prepare_shutdown'}
                ],
                'confidence_threshold': 0.5
            },
            
            # Time and date queries
            'question_time': {
                'actions': [
                    {'type': 'get_time'},
                    {'type': 'speak', 'template': 'time_response'}
                ],
                'confidence_threshold': 0.4
            },
            
            'question_date': {
                'actions': [
                    {'type': 'get_date'},
                    {'type': 'speak', 'template': 'date_response'}
                ],
                'confidence_threshold': 0.4
            },
            
            'question_weather': {
                'actions': [
                    {'type': 'get_weather'},
                    {'type': 'speak', 'template': 'weather_response'}
                ],
                'confidence_threshold': 0.4
            },
            
            # System control
            'system_shutdown': {
                'actions': [
                    {'type': 'speak', 'template': 'shutdown_confirmation'},
                    {'type': 'system', 'action': 'shutdown', 'requires_confirmation': True}
                ],
                'confidence_threshold': 0.7,
                'safety_check': True
            },
            
            'system_restart': {
                'actions': [
                    {'type': 'speak', 'template': 'restart_confirmation'},
                    {'type': 'system', 'action': 'restart', 'requires_confirmation': True}
                ],
                'confidence_threshold': 0.7,
                'safety_check': True
            },
            
            # Application control
            'open_application': {
                'actions': [
                    {'type': 'speak', 'template': 'opening_app'},
                    {'type': 'system', 'action': 'open_application'}
                ],
                'confidence_threshold': 0.5
            },
            
            'close_application': {
                'actions': [
                    {'type': 'speak', 'template': 'closing_app'},
                    {'type': 'system', 'action': 'close_application'}
                ],
                'confidence_threshold': 0.5
            },
            
            # Media control
            'play_music': {
                'actions': [
                    {'type': 'speak', 'template': 'playing_music'},
                    {'type': 'media', 'action': 'play'}
                ],
                'confidence_threshold': 0.4
            },
            
            'stop_music': {
                'actions': [
                    {'type': 'media', 'action': 'stop'},
                    {'type': 'speak', 'template': 'stopped_music'}
                ],
                'confidence_threshold': 0.4
            },
            
            # Search and information
            'search_web': {
                'actions': [
                    {'type': 'speak', 'template': 'searching_web'},
                    {'type': 'web', 'action': 'search'}
                ],
                'confidence_threshold': 0.4
            },
            
            'search_youtube': {
                'actions': [
                    {'type': 'speak', 'template': 'searching_youtube'},
                    {'type': 'web', 'action': 'youtube_search'}
                ],
                'confidence_threshold': 0.4
            },
            
            # Communication
            'send_message': {
                'actions': [
                    {'type': 'speak', 'template': 'sending_message'},
                    {'type': 'communication', 'action': 'send_message'}
                ],
                'confidence_threshold': 0.6
            },
            
            'make_call': {
                'actions': [
                    {'type': 'speak', 'template': 'making_call'},
                    {'type': 'communication', 'action': 'make_call'}
                ],
                'confidence_threshold': 0.6
            },
            
            # Memory and notes
            'remember_something': {
                'actions': [
                    {'type': 'memory', 'action': 'store'},
                    {'type': 'speak', 'template': 'remembered'}
                ],
                'confidence_threshold': 0.5
            },
            
            'recall_memory': {
                'actions': [
                    {'type': 'memory', 'action': 'retrieve'},
                    {'type': 'speak', 'template': 'recall_response'}
                ],
                'confidence_threshold': 0.4
            },
            
            # Help and information
            'help': {
                'actions': [
                    {'type': 'speak', 'template': 'help_response'}
                ],
                'confidence_threshold': 0.3
            },
            
            'who_are_you': {
                'actions': [
                    {'type': 'speak', 'template': 'introduction'}
                ],
                'confidence_threshold': 0.3
            },
            
            'how_are_you': {
                'actions': [
                    {'type': 'speak', 'template': 'status_response'}
                ],
                'confidence_threshold': 0.3
            },
            
            # Unknown intent
            'unknown': {
                'actions': [
                    {'type': 'speak', 'template': 'clarification_request'}
                ],
                'confidence_threshold': 0.0
            }
        }
    
    def _load_safety_policies(self) -> Dict[str, Any]:
        """Load safety policies for dangerous actions"""
        return {
            'system_shutdown': {
                'requires_confirmation': True,
                'confirmation_timeout': 10,
                'allowed_hours': None  # Always allowed
            },
            'system_restart': {
                'requires_confirmation': True,
                'confirmation_timeout': 10,
                'allowed_hours': None
            },
            'file_deletion': {
                'requires_confirmation': True,
                'confirmation_timeout': 15,
                'allowed_hours': [6, 22]  # Only during day hours
            },
            'send_message': {
                'requires_confirmation': False,
                'rate_limit': 10,  # Max 10 messages per hour
                'allowed_hours': [6, 23]
            },
            'make_call': {
                'requires_confirmation': True,
                'rate_limit': 5,  # Max 5 calls per hour
                'allowed_hours': [8, 21]  # Business hours
            }
        }
    
    async def _load_user_policies(self):
        """Load user-specific policies and preferences"""
        # This would typically load from a configuration file or database
        self.user_preferences = {
            'default': {
                'auto_confirm_safe_actions': True,
                'voice_feedback': True,
                'detailed_responses': False,
                'privacy_mode': False
            }
        }
    
    async def make_decision(self, 
                          intent_result, 
                          context: Dict[str, Any], 
                          memories: List[Any]) -> Decision:
        """
        Make a decision about what actions to take
        
        Args:
            intent_result: Result from intent recognition
            context: Current context information
            memories: Relevant memories
            
        Returns:
            Decision with actions to execute
        """
        try:
            intent = intent_result.intent
            confidence = intent_result.confidence
            entities = intent_result.entities
            
            # Get action mapping for this intent
            action_mapping = self.action_mappings.get(intent, self.action_mappings['unknown'])
            
            # Check confidence threshold
            required_confidence = action_mapping.get('confidence_threshold', 0.5)
            if confidence < required_confidence:
                # Fall back to clarification
                action_mapping = self.action_mappings['unknown']
                intent = 'unknown'
            
            # Apply safety checks
            safety_check_passed = await self._apply_safety_checks(
                intent, entities, context, action_mapping
            )
            
            if not safety_check_passed:
                return Decision(
                    actions=[Action(
                        action_type='speak',
                        parameters={'template': 'safety_denied'},
                        priority=1
                    )],
                    confidence=1.0,
                    reasoning="Action denied by safety policy",
                    requires_user_confirmation=False
                )
            
            # Build actions
            actions = await self._build_actions(
                intent, entities, context, memories, action_mapping
            )
            
            # Determine if confirmation is needed
            requires_confirmation = any(
                action.requires_confirmation for action in actions
            ) or action_mapping.get('safety_check', False)
            
            # Build reasoning
            reasoning = await self._build_reasoning(
                intent, confidence, entities, context, actions
            )
            
            decision = Decision(
                actions=actions,
                confidence=confidence,
                reasoning=reasoning,
                requires_user_confirmation=requires_confirmation,
                metadata={
                    'intent': intent,
                    'original_confidence': intent_result.confidence,
                    'entities': entities,
                    'timestamp': datetime.now().isoformat()
                }
            )
            
            self.logger.debug(f"Made decision for intent '{intent}' with {len(actions)} actions")
            return decision
            
        except Exception as e:
            self.logger.error(f"Error making decision: {e}")
            return Decision(
                actions=[Action(
                    action_type='speak',
                    parameters={'template': 'error_response'},
                    priority=1
                )],
                confidence=0.0,
                reasoning=f"Error in decision making: {str(e)}"
            )
    
    async def _apply_safety_checks(self, 
                                 intent: str, 
                                 entities: Dict[str, Any],
                                 context: Dict[str, Any],
                                 action_mapping: Dict[str, Any]) -> bool:
        """Apply safety policies to the intended action"""
        try:
            # Check if this intent has safety policies
            safety_policy = self.safety_policies.get(intent)
            if not safety_policy:
                return True  # No policy means allowed
            
            # Check time restrictions
            allowed_hours = safety_policy.get('allowed_hours')
            if allowed_hours:
                current_hour = context.get('temporal_context', {}).get('hour', 12)
                if not (allowed_hours[0] <= current_hour <= allowed_hours[1]):
                    self.logger.warning(f"Action '{intent}' denied due to time restriction")
                    return False
            
            # Check rate limits (would need to track usage)
            rate_limit = safety_policy.get('rate_limit')
            if rate_limit:
                # This would check against stored usage data
                # For now, we'll assume it passes
                pass
            
            # Check user preferences
            user_id = context.get('user_id', 'default')
            user_prefs = self.user_preferences.get(user_id, {})
            
            if user_prefs.get('privacy_mode', False):
                # In privacy mode, deny certain actions
                if intent in ['send_message', 'make_call', 'search_web']:
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error in safety check: {e}")
            return False  # Deny on error for safety
    
    async def _build_actions(self, 
                           intent: str,
                           entities: Dict[str, Any],
                           context: Dict[str, Any],
                           memories: List[Any],
                           action_mapping: Dict[str, Any]) -> List[Action]:
        """Build the list of actions to execute"""
        actions = []
        
        try:
            action_configs = action_mapping.get('actions', [])
            
            for i, action_config in enumerate(action_configs):
                action_type = action_config.get('type', 'unknown')
                
                # Build parameters based on action type
                parameters = await self._build_action_parameters(
                    action_type, action_config, intent, entities, context, memories
                )
                
                # Determine if confirmation is required
                requires_confirmation = action_config.get('requires_confirmation', False)
                
                # Estimate duration (simple heuristic)
                duration = self._estimate_action_duration(action_type, parameters)
                
                action = Action(
                    action_type=action_type,
                    parameters=parameters,
                    priority=i + 1,  # Sequential priority
                    requires_confirmation=requires_confirmation,
                    estimated_duration=duration
                )
                
                actions.append(action)
            
        except Exception as e:
            self.logger.error(f"Error building actions: {e}")
        
        return actions
    
    async def _build_action_parameters(self,
                                     action_type: str,
                                     action_config: Dict[str, Any],
                                     intent: str,
                                     entities: Dict[str, Any],
                                     context: Dict[str, Any],
                                     memories: List[Any]) -> Dict[str, Any]:
        """Build parameters for a specific action"""
        parameters = action_config.copy()
        
        # Add context information
        parameters['intent'] = intent
        parameters['entities'] = entities
        parameters['context'] = context
        
        # Add specific parameters based on action type
        if action_type == 'speak':
            template = parameters.get('template', 'default_response')
            parameters['template'] = template
            
        elif action_type == 'system':
            system_action = parameters.get('action')
            if system_action == 'open_application':
                app_name = entities.get('application', [''])[0] if entities.get('application') else ''
                parameters['application'] = app_name
            elif system_action == 'close_application':
                app_name = entities.get('application', [''])[0] if entities.get('application') else ''
                parameters['application'] = app_name
                
        elif action_type == 'web':
            web_action = parameters.get('action')
            if web_action in ['search', 'youtube_search']:
                query = entities.get('query', [''])[0] if entities.get('query') else context.get('current_input', '')
                parameters['query'] = query
                
        elif action_type == 'memory':
            memory_action = parameters.get('action')
            if memory_action == 'store':
                content = entities.get('content', [''])[0] if entities.get('content') else context.get('current_input', '')
                parameters['content'] = content
            elif memory_action == 'retrieve':
                query = entities.get('query', [''])[0] if entities.get('query') else context.get('current_input', '')
                parameters['query'] = query
                
        elif action_type == 'communication':
            comm_action = parameters.get('action')
            if comm_action == 'send_message':
                contact = entities.get('contact', [''])[0] if entities.get('contact') else ''
                message = entities.get('message', [''])[0] if entities.get('message') else ''
                parameters['contact'] = contact
                parameters['message'] = message
            elif comm_action == 'make_call':
                contact = entities.get('contact', [''])[0] if entities.get('contact') else ''
                parameters['contact'] = contact
        
        return parameters
    
    def _estimate_action_duration(self, action_type: str, parameters: Dict[str, Any]) -> float:
        """Estimate how long an action will take to execute"""
        duration_map = {
            'speak': 2.0,  # 2 seconds average
            'system': 5.0,  # 5 seconds for system actions
            'web': 3.0,    # 3 seconds for web actions
            'memory': 1.0,  # 1 second for memory operations
            'media': 2.0,   # 2 seconds for media control
            'communication': 10.0  # 10 seconds for communication
        }
        
        return duration_map.get(action_type, 1.0)
    
    async def _build_reasoning(self,
                             intent: str,
                             confidence: float,
                             entities: Dict[str, Any],
                             context: Dict[str, Any],
                             actions: List[Action]) -> str:
        """Build human-readable reasoning for the decision"""
        reasoning_parts = [
            f"Recognized intent '{intent}' with {confidence:.1%} confidence"
        ]
        
        if entities:
            entity_desc = ", ".join([f"{k}: {v}" for k, v in entities.items()])
            reasoning_parts.append(f"Extracted entities: {entity_desc}")
        
        if actions:
            action_desc = ", ".join([action.action_type for action in actions])
            reasoning_parts.append(f"Planned actions: {action_desc}")
        
        return ". ".join(reasoning_parts)
    
    async def update_from_feedback(self, feedback: Dict[str, Any]):
        """Update decision making based on user feedback"""
        try:
            # This would implement learning from feedback
            # For now, just log it
            self.logger.info(f"Received feedback: {feedback}")
            
            # Future implementation could:
            # - Adjust confidence thresholds
            # - Update action mappings
            # - Learn user preferences
            
        except Exception as e:
            self.logger.error(f"Error processing feedback: {e}")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get decision engine status"""
        return {
            'initialized': self.is_initialized,
            'total_intents': len(self.action_mappings),
            'safety_policies': len(self.safety_policies),
            'user_preferences': len(self.user_preferences)
        }
    
    async def shutdown(self):
        """Shutdown the decision engine"""
        try:
            self.is_initialized = False
            self.logger.info("Decision Engine shutdown complete")
        except Exception as e:
            self.logger.error(f"Error during Decision Engine shutdown: {e}")
