"""
Memory System for Enhanced JARVIS
Handles long-term and short-term memory, learning, and knowledge storage
"""

import asyncio
import logging
import sqlite3
import json
import pickle
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import numpy as np
from pathlib import Path

@dataclass
class Memory:
    """Individual memory entry"""
    id: str
    user_id: str
    content: str
    memory_type: str  # 'conversation', 'preference', 'fact', 'skill'
    importance: float
    timestamp: datetime
    tags: List[str]
    metadata: Dict[str, Any]
    access_count: int = 0
    last_accessed: Optional[datetime] = None

@dataclass
class MemoryQuery:
    """Memory query parameters"""
    query_text: str
    user_id: str
    memory_types: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    limit: int = 10
    min_importance: float = 0.0

class MemorySystem:
    """
    Advanced memory system for storing and retrieving contextual information
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Database configuration
        self.db_path = Path(config.get('db_path', 'enhanced_jarvis/data/memory.db'))
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Memory configuration
        self.max_memories_per_user = config.get('max_memories_per_user', 10000)
        self.importance_decay_rate = config.get('importance_decay_rate', 0.1)
        self.cleanup_interval = config.get('cleanup_interval', 3600)  # 1 hour
        
        # State
        self.is_initialized = False
        self._cleanup_task = None
        self._connection_pool = []
    
    async def initialize(self):
        """Initialize the memory system"""
        try:
            self.logger.info("Initializing Memory System...")
            
            # Create database tables
            await self._create_tables()
            
            # Start cleanup task
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
            
            self.is_initialized = True
            self.logger.info("Memory System initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Memory System: {e}")
            raise
    
    async def _create_tables(self):
        """Create database tables for memory storage"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Memories table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS memories (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                content TEXT NOT NULL,
                memory_type TEXT NOT NULL,
                importance REAL NOT NULL,
                timestamp TEXT NOT NULL,
                tags TEXT,
                metadata TEXT,
                access_count INTEGER DEFAULT 0,
                last_accessed TEXT,
                embedding BLOB
            )
        ''')
        
        # Interactions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS interactions (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                input_text TEXT NOT NULL,
                response_text TEXT NOT NULL,
                intent TEXT NOT NULL,
                context TEXT,
                timestamp TEXT NOT NULL,
                feedback_score REAL
            )
        ''')
        
        # User preferences table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_preferences (
                user_id TEXT NOT NULL,
                preference_key TEXT NOT NULL,
                preference_value TEXT NOT NULL,
                timestamp TEXT NOT NULL,
                PRIMARY KEY (user_id, preference_key)
            )
        ''')
        
        # Create indexes
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_memories_user_id ON memories(user_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_memories_type ON memories(memory_type)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_memories_importance ON memories(importance)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_interactions_user_id ON interactions(user_id)')
        
        conn.commit()
        conn.close()
    
    async def store_memory(self, 
                          user_id: str, 
                          content: str, 
                          memory_type: str,
                          importance: float = 0.5,
                          tags: List[str] = None,
                          metadata: Dict[str, Any] = None) -> str:
        """Store a new memory"""
        try:
            memory_id = f"mem_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
            
            memory = Memory(
                id=memory_id,
                user_id=user_id,
                content=content,
                memory_type=memory_type,
                importance=importance,
                timestamp=datetime.now(),
                tags=tags or [],
                metadata=metadata or {}
            )
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO memories 
                (id, user_id, content, memory_type, importance, timestamp, tags, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                memory.id,
                memory.user_id,
                memory.content,
                memory.memory_type,
                memory.importance,
                memory.timestamp.isoformat(),
                json.dumps(memory.tags),
                json.dumps(memory.metadata)
            ))
            
            conn.commit()
            conn.close()
            
            self.logger.debug(f"Stored memory {memory_id} for user {user_id}")
            return memory_id
            
        except Exception as e:
            self.logger.error(f"Error storing memory: {e}")
            raise
    
    async def retrieve_relevant_memories(self, 
                                       query_text: str, 
                                       intent: str,
                                       user_id: str,
                                       limit: int = 5) -> List[Memory]:
        """Retrieve memories relevant to the current query"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Simple keyword-based retrieval (can be enhanced with embeddings)
            query_words = query_text.lower().split()
            
            # Build SQL query for relevance
            sql = '''
                SELECT * FROM memories 
                WHERE user_id = ? 
                AND (
                    LOWER(content) LIKE ? 
                    OR memory_type = ?
                    OR tags LIKE ?
                )
                ORDER BY importance DESC, timestamp DESC
                LIMIT ?
            '''
            
            search_pattern = f"%{' '.join(query_words)}%"
            tag_pattern = f"%{intent}%"
            
            cursor.execute(sql, (user_id, search_pattern, intent, tag_pattern, limit))
            rows = cursor.fetchall()
            
            memories = []
            for row in rows:
                memory = Memory(
                    id=row[0],
                    user_id=row[1],
                    content=row[2],
                    memory_type=row[3],
                    importance=row[4],
                    timestamp=datetime.fromisoformat(row[5]),
                    tags=json.loads(row[6]) if row[6] else [],
                    metadata=json.loads(row[7]) if row[7] else {},
                    access_count=row[8],
                    last_accessed=datetime.fromisoformat(row[9]) if row[9] else None
                )
                memories.append(memory)
                
                # Update access count
                cursor.execute('''
                    UPDATE memories 
                    SET access_count = access_count + 1, last_accessed = ?
                    WHERE id = ?
                ''', (datetime.now().isoformat(), memory.id))
            
            conn.commit()
            conn.close()
            
            self.logger.debug(f"Retrieved {len(memories)} relevant memories for user {user_id}")
            return memories
            
        except Exception as e:
            self.logger.error(f"Error retrieving memories: {e}")
            return []
    
    async def store_interaction(self, 
                              user_id: str, 
                              input_text: str, 
                              response_text: str,
                              intent: str,
                              context: Dict[str, Any]):
        """Store a conversation interaction"""
        try:
            interaction_id = f"int_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO interactions 
                (id, user_id, input_text, response_text, intent, context, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                interaction_id,
                user_id,
                input_text,
                response_text,
                intent,
                json.dumps(context),
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
            # Also store as memory if important
            if len(input_text) > 10:  # Simple importance heuristic
                await self.store_memory(
                    user_id=user_id,
                    content=f"User asked: {input_text}",
                    memory_type="conversation",
                    importance=0.3,
                    tags=[intent],
                    metadata={'interaction_id': interaction_id}
                )
            
            self.logger.debug(f"Stored interaction {interaction_id}")
            
        except Exception as e:
            self.logger.error(f"Error storing interaction: {e}")
    
    async def store_feedback(self, interaction_id: str, feedback: Dict[str, Any]):
        """Store user feedback for an interaction"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            feedback_score = feedback.get('score', 0.5)
            
            cursor.execute('''
                UPDATE interactions 
                SET feedback_score = ?
                WHERE id = ?
            ''', (feedback_score, interaction_id))
            
            conn.commit()
            conn.close()
            
            self.logger.debug(f"Stored feedback for interaction {interaction_id}")
            
        except Exception as e:
            self.logger.error(f"Error storing feedback: {e}")
    
    async def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """Get all preferences for a user"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT preference_key, preference_value 
                FROM user_preferences 
                WHERE user_id = ?
            ''', (user_id,))
            
            rows = cursor.fetchall()
            conn.close()
            
            preferences = {}
            for key, value in rows:
                try:
                    preferences[key] = json.loads(value)
                except json.JSONDecodeError:
                    preferences[key] = value
            
            return preferences
            
        except Exception as e:
            self.logger.error(f"Error getting user preferences: {e}")
            return {}
    
    async def set_user_preference(self, user_id: str, key: str, value: Any):
        """Set a user preference"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO user_preferences 
                (user_id, preference_key, preference_value, timestamp)
                VALUES (?, ?, ?, ?)
            ''', (
                user_id,
                key,
                json.dumps(value),
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
            self.logger.debug(f"Set preference {key} for user {user_id}")
            
        except Exception as e:
            self.logger.error(f"Error setting user preference: {e}")
    
    async def _periodic_cleanup(self):
        """Periodically clean up old memories"""
        while self.is_initialized:
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._cleanup_old_memories()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in memory cleanup: {e}")
    
    async def _cleanup_old_memories(self):
        """Remove old, low-importance memories"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get memory count per user
            cursor.execute('''
                SELECT user_id, COUNT(*) 
                FROM memories 
                GROUP BY user_id
            ''')
            
            for user_id, count in cursor.fetchall():
                if count > self.max_memories_per_user:
                    # Remove oldest, least important memories
                    excess = count - self.max_memories_per_user
                    cursor.execute('''
                        DELETE FROM memories 
                        WHERE id IN (
                            SELECT id FROM memories 
                            WHERE user_id = ?
                            ORDER BY importance ASC, timestamp ASC
                            LIMIT ?
                        )
                    ''', (user_id, excess))
            
            conn.commit()
            conn.close()
            
            self.logger.debug("Completed memory cleanup")
            
        except Exception as e:
            self.logger.error(f"Error in memory cleanup: {e}")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get memory system status"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT COUNT(*) FROM memories')
            total_memories = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM interactions')
            total_interactions = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(DISTINCT user_id) FROM memories')
            unique_users = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'initialized': self.is_initialized,
                'total_memories': total_memories,
                'total_interactions': total_interactions,
                'unique_users': unique_users,
                'db_path': str(self.db_path)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting memory status: {e}")
            return {'error': str(e)}
    
    async def shutdown(self):
        """Shutdown the memory system"""
        try:
            self.is_initialized = False
            
            if self._cleanup_task:
                self._cleanup_task.cancel()
                try:
                    await self._cleanup_task
                except asyncio.CancelledError:
                    pass
            
            self.logger.info("Memory System shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during Memory System shutdown: {e}")
