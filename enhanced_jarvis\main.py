"""
Enhanced JARVIS - Main Application
Advanced AI Assistant with Context Awareness and Intelligence
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path
from typing import Dict, Any
import json
import argparse

# Core components
from core.ai_engine import AIEngine
from voice.speech_recognition import EnhancedSpeechRecognizer, VoiceCommand
from voice.text_to_speech import EnhancedTextToSpeech
from automation.task_executor import TaskExecutor

class EnhancedJARVIS:
    """
    Main Enhanced JARVIS Application
    """
    
    def __init__(self, config_path: str = None):
        self.config = self._load_config(config_path)
        self.logger = self._setup_logging()
        
        # Core components
        self.ai_engine = None
        self.speech_recognizer = None
        self.text_to_speech = None
        self.task_executor = None
        
        # State
        self.running = False
        self.initialized = False
    
    def _load_config(self, config_path: str = None) -> Dict[str, Any]:
        """Load configuration from file or use defaults"""
        default_config = {
            'ai_engine': {
                'context': {
                    'max_history_length': 50,
                    'cleanup_interval': 300
                },
                'memory': {
                    'db_path': 'enhanced_jarvis/data/memory.db',
                    'max_memories_per_user': 10000
                },
                'intent': {
                    'confidence_threshold': 0.3
                },
                'response': {
                    'personality': 'helpful',
                    'verbosity': 'moderate'
                },
                'decision': {
                    'safety_checks': True
                }
            },
            'speech_recognition': {
                'sample_rate': 16000,
                'language': 'en-US',
                'wake_words': ['jarvis', 'hey jarvis'],
                'continuous_listening': True,
                'noise_threshold': 300
            },
            'text_to_speech': {
                'engine': 'pyttsx3',
                'default_profile': 'default',
                'voice_profiles': {
                    'default': {
                        'voice_id': 0,
                        'rate': 150,
                        'volume': 0.9
                    }
                }
            },
            'task_executor': {
                'max_concurrent_tasks': 5,
                'timeout': 30
            },
            'logging': {
                'level': 'INFO',
                'file': 'enhanced_jarvis/logs/jarvis.log'
            }
        }
        
        if config_path and Path(config_path).exists():
            try:
                with open(config_path, 'r') as f:
                    user_config = json.load(f)
                # Merge user config with defaults
                self._deep_merge(default_config, user_config)
            except Exception as e:
                print(f"Error loading config: {e}, using defaults")
        
        return default_config
    
    def _deep_merge(self, base: Dict, update: Dict):
        """Deep merge two dictionaries"""
        for key, value in update.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._deep_merge(base[key], value)
            else:
                base[key] = value
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        log_config = self.config.get('logging', {})
        log_level = getattr(logging, log_config.get('level', 'INFO'))
        log_file = log_config.get('file', 'enhanced_jarvis/logs/jarvis.log')
        
        # Create logs directory
        Path(log_file).parent.mkdir(parents=True, exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        return logging.getLogger(__name__)
    
    async def initialize(self):
        """Initialize all JARVIS components"""
        try:
            self.logger.info("Initializing Enhanced JARVIS...")
            
            # Initialize AI Engine
            self.ai_engine = AIEngine(self.config['ai_engine'])
            await self.ai_engine.initialize()
            
            # Initialize Speech Recognition
            self.speech_recognizer = EnhancedSpeechRecognizer(self.config['speech_recognition'])
            await self.speech_recognizer.initialize()
            
            # Initialize Text-to-Speech
            self.text_to_speech = EnhancedTextToSpeech(self.config['text_to_speech'])
            await self.text_to_speech.initialize()
            
            # Initialize Task Executor
            self.task_executor = TaskExecutor(self.config['task_executor'])
            await self.task_executor.initialize()
            
            # Connect components
            await self._connect_components()
            
            self.initialized = True
            self.logger.info("Enhanced JARVIS initialized successfully!")
            
            # Welcome message
            await self.text_to_speech.speak(
                "Enhanced JARVIS is now online and ready to assist you!",
                emotion="friendly"
            )
            
        except Exception as e:
            self.logger.error(f"Failed to initialize JARVIS: {e}")
            raise
    
    async def _connect_components(self):
        """Connect components together"""
        # Register voice command callback
        self.speech_recognizer.add_command_callback(self._handle_voice_command)
        
        # Connect task executor to TTS for feedback
        self.task_executor.set_tts_engine(self.text_to_speech)
    
    async def _handle_voice_command(self, command: VoiceCommand):
        """Handle incoming voice commands"""
        try:
            self.logger.info(f"Processing voice command: {command.text}")
            
            # Process through AI engine
            result = await self.ai_engine.process_input(
                input_text=command.text,
                input_type="voice",
                user_id="default",
                metadata={
                    'confidence': command.confidence,
                    'audio_duration': command.audio_duration,
                    'language': command.language
                }
            )
            
            # Speak the response
            if result.response:
                await self.text_to_speech.speak(result.response)
            
            # Execute actions
            if result.actions:
                await self.task_executor.execute_actions(result.actions)
            
        except Exception as e:
            self.logger.error(f"Error handling voice command: {e}")
            await self.text_to_speech.speak(
                "I apologize, but I encountered an error processing your request.",
                emotion="apologetic"
            )
    
    async def run(self):
        """Run the main JARVIS loop"""
        if not self.initialized:
            await self.initialize()
        
        try:
            self.running = True
            self.logger.info("Starting Enhanced JARVIS main loop...")
            
            # Start continuous listening
            await self.speech_recognizer.start_listening()
            
            # Main loop
            while self.running:
                try:
                    # Check system status periodically
                    await self._check_system_status()
                    
                    # Sleep briefly to prevent busy waiting
                    await asyncio.sleep(1)
                    
                except KeyboardInterrupt:
                    self.logger.info("Received interrupt signal")
                    break
                except Exception as e:
                    self.logger.error(f"Error in main loop: {e}")
                    await asyncio.sleep(5)  # Brief pause before continuing
            
        except Exception as e:
            self.logger.error(f"Fatal error in main loop: {e}")
            raise
        finally:
            await self.shutdown()
    
    async def _check_system_status(self):
        """Periodically check system status"""
        # This could monitor system health, memory usage, etc.
        # For now, just ensure all components are still running
        pass
    
    async def process_text_input(self, text: str, user_id: str = "default") -> str:
        """Process text input (for testing or text interface)"""
        try:
            result = await self.ai_engine.process_input(
                input_text=text,
                input_type="text",
                user_id=user_id
            )
            
            # Execute actions if any
            if result.actions:
                await self.task_executor.execute_actions(result.actions)
            
            return result.response
            
        except Exception as e:
            self.logger.error(f"Error processing text input: {e}")
            return "I apologize, but I encountered an error processing your request."
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        status = {
            'running': self.running,
            'initialized': self.initialized,
            'components': {}
        }
        
        if self.ai_engine:
            status['components']['ai_engine'] = await self.ai_engine.get_system_status()
        
        if self.speech_recognizer:
            status['components']['speech_recognizer'] = await self.speech_recognizer.get_status()
        
        if self.text_to_speech:
            status['components']['text_to_speech'] = await self.text_to_speech.get_status()
        
        if self.task_executor:
            status['components']['task_executor'] = await self.task_executor.get_status()
        
        return status
    
    async def shutdown(self):
        """Gracefully shutdown JARVIS"""
        try:
            self.logger.info("Shutting down Enhanced JARVIS...")
            self.running = False
            
            # Farewell message
            if self.text_to_speech and self.text_to_speech.is_initialized:
                await self.text_to_speech.speak("Goodbye! JARVIS is shutting down.", emotion="warm")
                await asyncio.sleep(2)  # Give time for speech to complete
            
            # Shutdown components in reverse order
            if self.task_executor:
                await self.task_executor.shutdown()
            
            if self.speech_recognizer:
                await self.speech_recognizer.shutdown()
            
            if self.text_to_speech:
                await self.text_to_speech.shutdown()
            
            if self.ai_engine:
                await self.ai_engine.shutdown()
            
            self.logger.info("Enhanced JARVIS shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")

def setup_signal_handlers(jarvis_instance):
    """Setup signal handlers for graceful shutdown"""
    def signal_handler(signum, frame):
        print(f"\nReceived signal {signum}, shutting down gracefully...")
        asyncio.create_task(jarvis_instance.shutdown())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Enhanced JARVIS AI Assistant")
    parser.add_argument('--config', type=str, help='Path to configuration file')
    parser.add_argument('--text-mode', action='store_true', help='Run in text-only mode')
    parser.add_argument('--debug', action='store_true', help='Enable debug logging')
    
    args = parser.parse_args()
    
    # Create JARVIS instance
    jarvis = EnhancedJARVIS(config_path=args.config)
    
    # Setup signal handlers
    setup_signal_handlers(jarvis)
    
    try:
        if args.text_mode:
            # Text-only mode for testing
            await jarvis.initialize()
            print("Enhanced JARVIS - Text Mode")
            print("Type 'quit' to exit")
            
            while True:
                try:
                    user_input = input("\nYou: ").strip()
                    if user_input.lower() in ['quit', 'exit', 'bye']:
                        break
                    
                    if user_input:
                        response = await jarvis.process_text_input(user_input)
                        print(f"JARVIS: {response}")
                        
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    print(f"Error: {e}")
        else:
            # Full voice mode
            await jarvis.run()
            
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
