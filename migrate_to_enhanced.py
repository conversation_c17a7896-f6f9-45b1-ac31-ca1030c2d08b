#!/usr/bin/env python3
"""
Migration Script: Original JARVIS to Enhanced JARVIS
Migrates features and data from your original JARVIS9869.py to Enhanced JARVIS
"""

import os
import sys
import shutil
import json
from pathlib import Path

def print_banner():
    """Print migration banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║              JARVIS Migration Assistant                      ║
    ║         From Original JARVIS to Enhanced JARVIS             ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def backup_original_data():
    """Backup original JARVIS data"""
    print("📦 Backing up original JARVIS data...")
    
    backup_dir = Path("original_jarvis_backup")
    backup_dir.mkdir(exist_ok=True)
    
    files_to_backup = [
        "Data.txt",
        "data.txt", 
        "Goodnight.txt",
        "JARVIS9869.py",
        "Features.py",
        "Automations.py"
    ]
    
    for file_name in files_to_backup:
        if Path(file_name).exists():
            shutil.copy2(file_name, backup_dir / file_name)
            print(f"   ✅ Backed up: {file_name}")
    
    # Backup DataBase directory
    if Path("DataBase").exists():
        shutil.copytree("DataBase", backup_dir / "DataBase", dirs_exist_ok=True)
        print("   ✅ Backed up: DataBase directory")
    
    print(f"✅ Backup completed in: {backup_dir}")

def migrate_memory_data():
    """Migrate memory data from original JARVIS"""
    print("\n🧠 Migrating memory data...")
    
    memories = []
    
    # Migrate from data.txt
    if Path("data.txt").exists():
        try:
            with open("data.txt", "r", encoding="utf-8") as f:
                content = f.read().strip()
                if content:
                    memories.append({
                        "content": content,
                        "type": "user_note",
                        "source": "data.txt"
                    })
                    print(f"   ✅ Migrated note: {content[:50]}...")
        except Exception as e:
            print(f"   ⚠️  Error reading data.txt: {e}")
    
    # Migrate from Data.txt (if different)
    if Path("Data.txt").exists() and Path("Data.txt") != Path("data.txt"):
        try:
            with open("Data.txt", "r", encoding="utf-8") as f:
                content = f.read().strip()
                if content:
                    memories.append({
                        "content": content,
                        "type": "search_history",
                        "source": "Data.txt"
                    })
                    print(f"   ✅ Migrated search data: {content[:50]}...")
        except Exception as e:
            print(f"   ⚠️  Error reading Data.txt: {e}")
    
    # Save migration data for Enhanced JARVIS
    if memories:
        migration_file = Path("enhanced_jarvis/data/migration_data.json")
        migration_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(migration_file, "w", encoding="utf-8") as f:
            json.dump(memories, f, indent=2, ensure_ascii=False)
        
        print(f"   ✅ Migration data saved: {migration_file}")
    else:
        print("   ℹ️  No memory data found to migrate")

def create_enhanced_config():
    """Create Enhanced JARVIS config based on original settings"""
    print("\n⚙️  Creating Enhanced JARVIS configuration...")
    
    # Analyze original JARVIS settings
    original_config = {
        "voice_id": 0,  # From voices[0].id
        "rate": 145,    # From engine.setProperty('rate',145)
        "language": "en-in",  # From recognize_google language
        "wake_words": ["jarvis"]  # Inferred from code
    }
    
    # Create enhanced config
    enhanced_config = {
        "ai_engine": {
            "context": {
                "max_history_length": 50,
                "cleanup_interval": 300
            },
            "memory": {
                "db_path": "enhanced_jarvis/data/memory.db",
                "max_memories_per_user": 10000,
                "migration_file": "enhanced_jarvis/data/migration_data.json"
            },
            "intent": {
                "confidence_threshold": 0.3
            },
            "response": {
                "personality": "helpful",
                "verbosity": "moderate"
            }
        },
        "speech_recognition": {
            "sample_rate": 16000,
            "language": original_config["language"],
            "wake_words": original_config["wake_words"] + ["hey jarvis"],
            "continuous_listening": True,
            "noise_threshold": 300
        },
        "text_to_speech": {
            "engine": "pyttsx3",
            "default_profile": "jarvis_classic",
            "voice_profiles": {
                "jarvis_classic": {
                    "voice_id": original_config["voice_id"],
                    "rate": original_config["rate"],
                    "volume": 0.9,
                    "emotion_modifiers": {
                        "happy": {"rate": 160, "pitch": 10},
                        "calm": {"rate": 140, "pitch": -5}
                    }
                }
            }
        },
        "task_executor": {
            "max_concurrent_tasks": 5,
            "timeout": 30
        },
        "logging": {
            "level": "INFO",
            "file": "enhanced_jarvis/logs/jarvis.log"
        }
    }
    
    # Save config
    config_file = Path("enhanced_jarvis/config/migrated_config.json")
    config_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(config_file, "w") as f:
        json.dump(enhanced_config, f, indent=2)
    
    print(f"   ✅ Enhanced config created: {config_file}")
    return config_file

def create_feature_mapping():
    """Create mapping of original features to enhanced features"""
    print("\n🔗 Creating feature mapping...")
    
    feature_mapping = {
        "Original Feature": "Enhanced JARVIS Equivalent",
        "Basic voice recognition": "✅ Enhanced with wake word detection",
        "Simple TTS": "✅ Advanced TTS with emotions",
        "Google search": "✅ Improved web search with context",
        "YouTube search": "✅ Enhanced YouTube integration",
        "WhatsApp messaging": "🔄 Available via task executor",
        "System shutdown/restart": "✅ Enhanced with safety confirmations",
        "Screenshot capture": "🔄 Can be added as plugin",
        "News reading": "🔄 Available via API integration",
        "Time/Date queries": "✅ Enhanced with context awareness",
        "Weather information": "✅ Improved with location context",
        "Simple memory (data.txt)": "✅ Advanced SQLite-based memory",
        "Application control": "✅ Enhanced with better detection",
        "Media control": "✅ Improved media management",
        "Volume control": "🔄 Available via system integration",
        "Basic conversation": "✅ Context-aware conversations"
    }
    
    # Save mapping
    mapping_file = Path("enhanced_jarvis/docs/feature_mapping.json")
    mapping_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(mapping_file, "w") as f:
        json.dump(feature_mapping, f, indent=2)
    
    print("   ✅ Feature mapping created")
    
    # Print summary
    print("\n📋 Feature Migration Summary:")
    for original, enhanced in feature_mapping.items():
        if original != "Original Feature":
            status = "✅" if enhanced.startswith("✅") else "🔄"
            print(f"   {status} {original}")

def create_migration_guide():
    """Create migration guide"""
    print("\n📚 Creating migration guide...")
    
    guide = """# Migration Guide: Original JARVIS to Enhanced JARVIS

## What's Been Migrated

### ✅ Automatically Migrated
- Voice recognition settings (language, rate)
- Memory data from data.txt files
- Basic configuration preferences

### 🔄 Available but Needs Setup
- WhatsApp integration (via automation plugins)
- Screenshot functionality (via task executor)
- Volume control (via system integration)
- News reading (via API configuration)

### ✅ Enhanced Features
- Context-aware conversations
- Intelligent memory system
- Advanced intent recognition
- Safety policies and confirmations
- Better error handling

## Quick Start

1. **Test Enhanced JARVIS:**
   ```bash
   python -m enhanced_jarvis.main --text-mode --config enhanced_jarvis/config/migrated_config.json
   ```

2. **Try Voice Commands:**
   - "Hello Jarvis" (basic greeting)
   - "What do you remember?" (test migrated memory)
   - "Search for Python tutorials" (web search)
   - "What time is it?" (time query)

3. **Compare with Original:**
   - Run both systems side by side
   - Test the same commands in both
   - Notice the improved responses

## Adding Missing Features

### WhatsApp Integration
Add to task_executor.py:
```python
async def _send_whatsapp_message(self, contact, message):
    # Your WhatsApp automation code here
    pass
```

### Screenshot Functionality
Add to task_executor.py:
```python
async def _take_screenshot(self, filename):
    # Your screenshot code here
    pass
```

## Configuration

Edit `enhanced_jarvis/config/migrated_config.json` to:
- Adjust voice settings
- Modify wake words
- Change personality traits
- Configure safety policies

## Troubleshooting

If Enhanced JARVIS doesn't work as expected:
1. Check the logs: `enhanced_jarvis/logs/jarvis.log`
2. Test in text mode first
3. Verify microphone permissions
4. Check dependency installation

Your original JARVIS is backed up in `original_jarvis_backup/`
"""
    
    guide_file = Path("MIGRATION_GUIDE.md")
    with open(guide_file, "w") as f:
        f.write(guide)
    
    print(f"   ✅ Migration guide created: {guide_file}")

def main():
    """Main migration function"""
    print_banner()
    
    print("This script will help you migrate from your original JARVIS to Enhanced JARVIS.")
    print("Your original files will be safely backed up.")
    
    response = input("\n🤔 Do you want to proceed with migration? (y/n): ").lower().strip()
    if response != 'y':
        print("Migration cancelled.")
        return
    
    try:
        # Step 1: Backup original data
        backup_original_data()
        
        # Step 2: Migrate memory data
        migrate_memory_data()
        
        # Step 3: Create enhanced config
        config_file = create_enhanced_config()
        
        # Step 4: Create feature mapping
        create_feature_mapping()
        
        # Step 5: Create migration guide
        create_migration_guide()
        
        # Success message
        print("\n" + "="*60)
        print("🎉 Migration Completed Successfully!")
        print("="*60)
        print(f"✅ Original data backed up")
        print(f"✅ Memory data migrated")
        print(f"✅ Enhanced config created")
        print(f"✅ Feature mapping documented")
        print(f"✅ Migration guide created")
        
        print(f"\n🚀 Next Steps:")
        print(f"1. Install Enhanced JARVIS:")
        print(f"   cd enhanced_jarvis && python install.py")
        print(f"")
        print(f"2. Test Enhanced JARVIS:")
        print(f"   python -m enhanced_jarvis.main --text-mode --config {config_file}")
        print(f"")
        print(f"3. Read the migration guide:")
        print(f"   Open MIGRATION_GUIDE.md for detailed instructions")
        
    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        print("Your original files are safe and unchanged.")

if __name__ == "__main__":
    main()
