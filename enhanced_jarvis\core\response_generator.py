"""
Response Generator for Enhanced JARVIS
Generates natural, contextual responses based on intent and context
"""

import asyncio
import logging
import random
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import json
import re

@dataclass
class ResponseResult:
    """Generated response result"""
    text: str
    emotion: str
    confidence: float
    processing_time: float
    metadata: Dict[str, Any]

class ResponseGenerator:
    """
    Intelligent response generation with context awareness
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Response templates and patterns
        self.response_templates = self._load_response_templates()
        self.personality_config = self._load_personality_config()
        
        # State
        self.is_initialized = False
    
    async def initialize(self):
        """Initialize the response generator"""
        try:
            self.logger.info("Initializing Response Generator...")
            
            # Load any additional templates or models
            await self._load_dynamic_templates()
            
            self.is_initialized = True
            self.logger.info("Response Generator initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Response Generator: {e}")
            raise
    
    def _load_response_templates(self) -> Dict[str, Any]:
        """Load response templates for different intents and situations"""
        return {
            # Greeting responses
            'greeting_response': {
                'templates': [
                    "Hello! How can I assist you today?",
                    "Hi there! What can I do for you?",
                    "Good {time_of_day}! I'm here to help.",
                    "Hello! Ready to help with whatever you need.",
                    "Hi! What's on your agenda today?"
                ],
                'context_variants': {
                    'morning': [
                        "Good morning! Hope you're having a great start to your day.",
                        "Morning! Ready to tackle the day together?",
                        "Good morning! What's first on your list today?"
                    ],
                    'afternoon': [
                        "Good afternoon! How's your day going so far?",
                        "Afternoon! What can I help you with?",
                        "Good afternoon! Ready to assist with your tasks."
                    ],
                    'evening': [
                        "Good evening! How can I help you wind down?",
                        "Evening! What can I do for you tonight?",
                        "Good evening! Ready to assist with your evening tasks."
                    ]
                }
            },
            
            # Goodbye responses
            'goodbye_response': {
                'templates': [
                    "Goodbye! Have a wonderful {time_period}!",
                    "See you later! Take care!",
                    "Farewell! Don't hesitate to call if you need anything.",
                    "Goodbye! It was great helping you today.",
                    "Until next time! Have a great {time_period}!"
                ],
                'context_variants': {
                    'night': [
                        "Good night! Sleep well!",
                        "Sweet dreams! See you tomorrow!",
                        "Good night! Rest well!"
                    ]
                }
            },
            
            # Time responses
            'time_response': {
                'templates': [
                    "It's currently {time}.",
                    "The time is {time}.",
                    "Right now it's {time}.",
                    "The current time is {time}."
                ]
            },
            
            # Date responses
            'date_response': {
                'templates': [
                    "Today is {date}.",
                    "The date is {date}.",
                    "Today's date is {date}.",
                    "It's {date} today."
                ]
            },
            
            # Weather responses
            'weather_response': {
                'templates': [
                    "The weather {location} is {weather}.",
                    "Currently {location}, it's {weather}.",
                    "Weather conditions {location}: {weather}.",
                    "It's {weather} {location} right now."
                ]
            },
            
            # System action confirmations
            'shutdown_confirmation': {
                'templates': [
                    "I'll shut down the system in a moment. Is that okay?",
                    "Preparing to shut down. Should I proceed?",
                    "About to shut down the system. Confirm?",
                    "Ready to shut down. Is this what you want?"
                ]
            },
            
            'restart_confirmation': {
                'templates': [
                    "I'll restart the system. Is that correct?",
                    "Preparing to restart. Should I proceed?",
                    "About to restart the system. Confirm?",
                    "Ready to restart. Is this what you want?"
                ]
            },
            
            # Application actions
            'opening_app': {
                'templates': [
                    "Opening {application} for you.",
                    "Launching {application} now.",
                    "Starting {application}.",
                    "Getting {application} ready for you."
                ]
            },
            
            'closing_app': {
                'templates': [
                    "Closing {application}.",
                    "Shutting down {application}.",
                    "Ending {application} session.",
                    "Closing {application} for you."
                ]
            },
            
            # Media control
            'playing_music': {
                'templates': [
                    "Playing music for you.",
                    "Starting your music.",
                    "Here's some music to enjoy.",
                    "Music is now playing."
                ]
            },
            
            'stopped_music': {
                'templates': [
                    "Music stopped.",
                    "Paused your music.",
                    "Music is now off.",
                    "Stopped playing music."
                ]
            },
            
            # Search actions
            'searching_web': {
                'templates': [
                    "Searching the web for {query}.",
                    "Looking up {query} for you.",
                    "Finding information about {query}.",
                    "Searching for {query} online."
                ]
            },
            
            'searching_youtube': {
                'templates': [
                    "Searching YouTube for {query}.",
                    "Looking for {query} videos.",
                    "Finding {query} on YouTube.",
                    "Searching YouTube: {query}."
                ]
            },
            
            # Communication
            'sending_message': {
                'templates': [
                    "Sending message to {contact}.",
                    "Messaging {contact} for you.",
                    "Sending your message to {contact}.",
                    "Message to {contact} is being sent."
                ]
            },
            
            'making_call': {
                'templates': [
                    "Calling {contact} now.",
                    "Placing call to {contact}.",
                    "Dialing {contact} for you.",
                    "Connecting you with {contact}."
                ]
            },
            
            # Memory operations
            'remembered': {
                'templates': [
                    "I've made a note of that.",
                    "Remembered! I'll keep that in mind.",
                    "Got it! I've stored that information.",
                    "Noted and remembered.",
                    "I'll remember that for you."
                ]
            },
            
            'recall_response': {
                'templates': [
                    "Here's what I remember: {memory_content}",
                    "I recall: {memory_content}",
                    "From my memory: {memory_content}",
                    "I remember you mentioned: {memory_content}"
                ]
            },
            
            # Help and information
            'help_response': {
                'templates': [
                    "I can help you with many things! I can control your system, search the web, play music, send messages, remember things for you, and much more. What would you like to do?",
                    "I'm here to assist! I can manage applications, search for information, control media, handle communications, and remember important details. How can I help?",
                    "I have many capabilities! System control, web searches, media management, messaging, note-taking, and more. What do you need help with?",
                    "I'm your digital assistant! I can help with system tasks, information searches, entertainment, communication, and memory. What shall we do?"
                ]
            },
            
            'introduction': {
                'templates': [
                    "I'm JARVIS, your enhanced AI assistant. I'm here to help you with various tasks and make your digital life easier.",
                    "Hello! I'm JARVIS, an advanced AI assistant designed to help you with system control, information retrieval, and task automation.",
                    "I'm JARVIS, your intelligent digital companion. I can assist with a wide range of tasks to make your day more productive.",
                    "Greetings! I'm JARVIS, your AI assistant. I'm designed to understand your needs and help you accomplish your goals efficiently."
                ]
            },
            
            'status_response': {
                'templates': [
                    "I'm doing great! All systems are running smoothly and I'm ready to help.",
                    "I'm functioning perfectly! How can I assist you today?",
                    "All systems operational! I'm here and ready to help with whatever you need.",
                    "I'm doing well, thank you for asking! What can I do for you?"
                ]
            },
            
            # Error and clarification
            'clarification_request': {
                'templates': [
                    "I'm not sure I understood that. Could you please rephrase?",
                    "Could you clarify what you'd like me to do?",
                    "I didn't quite catch that. Can you try again?",
                    "I'm not certain what you mean. Could you be more specific?",
                    "Can you help me understand what you're looking for?"
                ]
            },
            
            'error_response': {
                'templates': [
                    "I apologize, but I encountered an error. Please try again.",
                    "Something went wrong. Let me try to help you differently.",
                    "I'm having trouble with that request. Can you try rephrasing?",
                    "There was an issue processing your request. Please try again."
                ]
            },
            
            'safety_denied': {
                'templates': [
                    "I can't perform that action for safety reasons.",
                    "That action is restricted by my safety protocols.",
                    "I'm not able to do that to keep your system safe.",
                    "For security reasons, I cannot complete that request."
                ]
            }
        }
    
    def _load_personality_config(self) -> Dict[str, Any]:
        """Load personality configuration for response style"""
        return {
            'tone': 'helpful',  # helpful, formal, casual, friendly
            'verbosity': 'moderate',  # brief, moderate, detailed
            'personality_traits': [
                'helpful',
                'intelligent',
                'reliable',
                'respectful',
                'efficient'
            ],
            'emotional_range': {
                'enthusiasm': 0.7,
                'empathy': 0.8,
                'humor': 0.3,
                'formality': 0.6
            }
        }
    
    async def _load_dynamic_templates(self):
        """Load dynamic templates based on user preferences or context"""
        # This could load user-specific templates or learn from interactions
        pass
    
    async def generate_response(self,
                              intent_result,
                              decision,
                              context: Dict[str, Any],
                              memories: List[Any]) -> ResponseResult:
        """
        Generate a natural response based on intent and context
        
        Args:
            intent_result: Result from intent recognition
            decision: Decision from decision engine
            context: Current context
            memories: Relevant memories
            
        Returns:
            ResponseResult with generated response
        """
        start_time = datetime.now()
        
        try:
            # Determine response template
            template_key = await self._determine_template_key(intent_result, decision, context)
            
            # Get template configuration
            template_config = self.response_templates.get(template_key, {})
            
            # Select appropriate template variant
            template = await self._select_template_variant(template_config, context)
            
            # Fill template with context data
            response_text = await self._fill_template(template, intent_result, decision, context, memories)
            
            # Apply personality adjustments
            response_text = await self._apply_personality(response_text, context)
            
            # Determine emotion
            emotion = await self._determine_emotion(intent_result, context)
            
            # Calculate confidence
            confidence = await self._calculate_response_confidence(intent_result, template_config)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = ResponseResult(
                text=response_text,
                emotion=emotion,
                confidence=confidence,
                processing_time=processing_time,
                metadata={
                    'template_key': template_key,
                    'intent': intent_result.intent,
                    'timestamp': datetime.now().isoformat()
                }
            )
            
            self.logger.debug(f"Generated response for intent '{intent_result.intent}'")
            return result
            
        except Exception as e:
            self.logger.error(f"Error generating response: {e}")
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return ResponseResult(
                text="I apologize, but I'm having trouble formulating a response right now.",
                emotion="apologetic",
                confidence=0.0,
                processing_time=processing_time,
                metadata={'error': str(e)}
            )
    
    async def _determine_template_key(self, intent_result, decision, context: Dict[str, Any]) -> str:
        """Determine which template to use based on intent and decision"""
        # Check if decision has specific actions that need responses
        if decision.actions:
            first_action = decision.actions[0]
            if first_action.action_type == 'speak':
                template_key = first_action.parameters.get('template', 'default_response')
                if template_key in self.response_templates:
                    return template_key
        
        # Fall back to intent-based template
        intent = intent_result.intent
        intent_template_map = {
            'greeting': 'greeting_response',
            'goodbye': 'goodbye_response',
            'question_time': 'time_response',
            'question_date': 'date_response',
            'question_weather': 'weather_response',
            'help': 'help_response',
            'who_are_you': 'introduction',
            'how_are_you': 'status_response',
            'unknown': 'clarification_request'
        }
        
        return intent_template_map.get(intent, 'clarification_request')
    
    async def _select_template_variant(self, template_config: Dict[str, Any], context: Dict[str, Any]) -> str:
        """Select the most appropriate template variant based on context"""
        templates = template_config.get('templates', ["I'm here to help."])
        context_variants = template_config.get('context_variants', {})
        
        # Check for time-based variants
        temporal_context = context.get('temporal_context', {})
        hour = temporal_context.get('hour', 12)
        
        if hour < 12 and 'morning' in context_variants:
            templates = context_variants['morning']
        elif 12 <= hour < 18 and 'afternoon' in context_variants:
            templates = context_variants['afternoon']
        elif hour >= 18 and 'evening' in context_variants:
            templates = context_variants['evening']
        elif hour >= 22 or hour < 6 and 'night' in context_variants:
            templates = context_variants['night']
        
        # Select random template for variety
        return random.choice(templates)
    
    async def _fill_template(self,
                           template: str,
                           intent_result,
                           decision,
                           context: Dict[str, Any],
                           memories: List[Any]) -> str:
        """Fill template with actual data"""
        response = template
        
        try:
            # Time-based replacements
            temporal_context = context.get('temporal_context', {})
            current_time = temporal_context.get('current_time', datetime.now().isoformat())
            
            if '{time}' in response:
                time_str = datetime.now().strftime("%I:%M %p")
                response = response.replace('{time}', time_str)
            
            if '{date}' in response:
                date_str = datetime.now().strftime("%A, %B %d, %Y")
                response = response.replace('{date}', date_str)
            
            if '{time_of_day}' in response:
                hour = temporal_context.get('hour', 12)
                if hour < 12:
                    time_of_day = "morning"
                elif hour < 18:
                    time_of_day = "afternoon"
                else:
                    time_of_day = "evening"
                response = response.replace('{time_of_day}', time_of_day)
            
            if '{time_period}' in response:
                hour = temporal_context.get('hour', 12)
                if hour < 6 or hour >= 22:
                    time_period = "night"
                elif hour < 12:
                    time_period = "day"
                elif hour < 18:
                    time_period = "afternoon"
                else:
                    time_period = "evening"
                response = response.replace('{time_period}', time_period)
            
            # Entity-based replacements
            entities = intent_result.entities
            
            for entity_type, entity_values in entities.items():
                placeholder = f'{{{entity_type}}}'
                if placeholder in response and entity_values:
                    entity_value = entity_values[0] if isinstance(entity_values, list) else entity_values
                    response = response.replace(placeholder, str(entity_value))
            
            # Action-based replacements
            if decision.actions:
                for action in decision.actions:
                    params = action.parameters
                    for key, value in params.items():
                        placeholder = f'{{{key}}}'
                        if placeholder in response:
                            response = response.replace(placeholder, str(value))
            
            # Memory-based replacements
            if '{memory_content}' in response and memories:
                memory_content = memories[0].content if memories else "nothing specific"
                response = response.replace('{memory_content}', memory_content)
            
            # Location-based replacements
            if '{location}' in response:
                location = entities.get('location', ['here'])[0] if entities.get('location') else 'here'
                response = response.replace('{location}', f"in {location}")
            
            # Weather placeholder (would be filled by actual weather data)
            if '{weather}' in response:
                response = response.replace('{weather}', "pleasant")
            
        except Exception as e:
            self.logger.error(f"Error filling template: {e}")
        
        return response
    
    async def _apply_personality(self, response: str, context: Dict[str, Any]) -> str:
        """Apply personality traits to the response"""
        personality = self.personality_config
        
        # Adjust based on verbosity setting
        verbosity = personality.get('verbosity', 'moderate')
        if verbosity == 'brief':
            # Make response more concise
            response = re.sub(r'\s+(Please|Thank you|I hope)', '', response)
        elif verbosity == 'detailed':
            # Add more context or explanation
            if not response.endswith('.'):
                response += '.'
            if 'help' in response.lower():
                response += " Feel free to ask if you need more information."
        
        # Apply tone adjustments
        tone = personality.get('tone', 'helpful')
        if tone == 'casual':
            response = response.replace('I will', "I'll")
            response = response.replace('cannot', "can't")
            response = response.replace('do not', "don't")
        elif tone == 'formal':
            response = response.replace("I'll", 'I will')
            response = response.replace("can't", 'cannot')
            response = response.replace("don't", 'do not')
        
        return response
    
    async def _determine_emotion(self, intent_result, context: Dict[str, Any]) -> str:
        """Determine the emotional tone of the response"""
        intent = intent_result.intent
        confidence = intent_result.confidence
        
        emotion_map = {
            'greeting': 'friendly',
            'goodbye': 'warm',
            'help': 'helpful',
            'question_time': 'informative',
            'question_date': 'informative',
            'question_weather': 'informative',
            'system_shutdown': 'cautious',
            'system_restart': 'cautious',
            'play_music': 'enthusiastic',
            'search_web': 'helpful',
            'remember_something': 'attentive',
            'how_are_you': 'friendly',
            'who_are_you': 'confident',
            'unknown': 'curious'
        }
        
        base_emotion = emotion_map.get(intent, 'neutral')
        
        # Adjust based on confidence
        if confidence < 0.3:
            base_emotion = 'uncertain'
        elif confidence > 0.8:
            if base_emotion == 'helpful':
                base_emotion = 'confident'
        
        return base_emotion
    
    async def _calculate_response_confidence(self, intent_result, template_config: Dict[str, Any]) -> float:
        """Calculate confidence in the generated response"""
        # Base confidence on intent recognition confidence
        base_confidence = intent_result.confidence
        
        # Adjust based on template availability
        if template_config:
            template_confidence = 0.9
        else:
            template_confidence = 0.5
        
        # Combine confidences
        final_confidence = (base_confidence * 0.7) + (template_confidence * 0.3)
        
        return min(1.0, final_confidence)
    
    async def get_status(self) -> Dict[str, Any]:
        """Get response generator status"""
        return {
            'initialized': self.is_initialized,
            'total_templates': len(self.response_templates),
            'personality_traits': len(self.personality_config.get('personality_traits', [])),
            'tone': self.personality_config.get('tone', 'helpful')
        }
    
    async def shutdown(self):
        """Shutdown the response generator"""
        try:
            self.is_initialized = False
            self.logger.info("Response Generator shutdown complete")
        except Exception as e:
            self.logger.error(f"Error during Response Generator shutdown: {e}")
