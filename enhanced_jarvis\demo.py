#!/usr/bin/env python3
"""
Enhanced JARVIS Demo Script
Demonstrates the capabilities of Enhanced JARVIS
"""

import asyncio
import sys
from pathlib import Path

# Add the enhanced_jarvis directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from main import EnhancedJARVIS

class JARVISDemo:
    """Demo class for Enhanced JARVIS"""
    
    def __init__(self):
        self.jarvis = None
    
    async def initialize(self):
        """Initialize JARVIS for demo"""
        print("🤖 Initializing Enhanced JARVIS for demo...")
        
        # Create JARVIS instance with demo config
        demo_config = {
            "ai_engine": {
                "context": {"max_history_length": 20},
                "memory": {"db_path": "demo_memory.db"},
                "intent": {"confidence_threshold": 0.2},
                "response": {"personality": "friendly", "verbosity": "detailed"}
            },
            "speech_recognition": {
                "continuous_listening": False,  # Manual for demo
                "wake_words": ["jarvis", "hey jarvis"]
            },
            "text_to_speech": {
                "engine": "pyttsx3",
                "default_profile": "demo"
            },
            "task_executor": {
                "max_concurrent_tasks": 3,
                "timeout": 20
            },
            "logging": {"level": "WARNING"}  # Reduce noise for demo
        }
        
        self.jarvis = EnhancedJARVIS()
        self.jarvis.config = demo_config
        
        try:
            await self.jarvis.initialize()
            print("✅ JARVIS initialized successfully!")
            return True
        except Exception as e:
            print(f"❌ Failed to initialize JARVIS: {e}")
            return False
    
    async def run_text_demo(self):
        """Run text-based demo"""
        print("\n" + "="*60)
        print("🎯 Enhanced JARVIS - Interactive Text Demo")
        print("="*60)
        print("Type commands to interact with JARVIS")
        print("Type 'demo' to run automated demo")
        print("Type 'quit' to exit")
        print("-"*60)
        
        while True:
            try:
                user_input = input("\n👤 You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    print("👋 Goodbye!")
                    break
                elif user_input.lower() == 'demo':
                    await self.run_automated_demo()
                    continue
                elif not user_input:
                    continue
                
                # Process input through JARVIS
                print("🤖 JARVIS: ", end="", flush=True)
                response = await self.jarvis.process_text_input(user_input)
                print(response)
                
            except KeyboardInterrupt:
                print("\n👋 Demo interrupted. Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    async def run_automated_demo(self):
        """Run automated demo with predefined commands"""
        print("\n🎬 Running Automated Demo...")
        print("-"*40)
        
        demo_commands = [
            ("Hello Jarvis", "Basic greeting"),
            ("What time is it?", "Time query"),
            ("What's the date today?", "Date query"),
            ("Remember that I like pizza", "Memory storage"),
            ("What do you remember about me?", "Memory retrieval"),
            ("Search for Python tutorials", "Web search"),
            ("Open calculator", "Application control"),
            ("How are you doing?", "Status check"),
            ("What can you help me with?", "Capabilities inquiry"),
            ("Thank you", "Polite interaction")
        ]
        
        for i, (command, description) in enumerate(demo_commands, 1):
            print(f"\n[{i}/{len(demo_commands)}] {description}")
            print(f"👤 User: {command}")
            print("🤖 JARVIS: ", end="", flush=True)
            
            try:
                response = await self.jarvis.process_text_input(command)
                print(response)
                
                # Brief pause between commands
                await asyncio.sleep(1)
                
            except Exception as e:
                print(f"Error processing '{command}': {e}")
        
        print("\n✅ Automated demo completed!")
        print("-"*40)
    
    async def demonstrate_features(self):
        """Demonstrate specific JARVIS features"""
        print("\n🔍 Feature Demonstration")
        print("="*50)
        
        # Context awareness demo
        print("\n1. Context Awareness:")
        await self._demo_context_awareness()
        
        # Memory system demo
        print("\n2. Memory System:")
        await self._demo_memory_system()
        
        # Intent recognition demo
        print("\n3. Intent Recognition:")
        await self._demo_intent_recognition()
        
        # Task execution demo
        print("\n4. Task Execution:")
        await self._demo_task_execution()
    
    async def _demo_context_awareness(self):
        """Demo context awareness"""
        commands = [
            "My name is John",
            "What's my name?",
            "I'm working on a Python project",
            "What am I working on?"
        ]
        
        for cmd in commands:
            print(f"   👤 {cmd}")
            response = await self.jarvis.process_text_input(cmd)
            print(f"   🤖 {response}")
    
    async def _demo_memory_system(self):
        """Demo memory system"""
        commands = [
            "Remember that my favorite color is blue",
            "Note that I have a meeting at 3 PM",
            "What's my favorite color?",
            "Do you remember anything about my schedule?"
        ]
        
        for cmd in commands:
            print(f"   👤 {cmd}")
            response = await self.jarvis.process_text_input(cmd)
            print(f"   🤖 {response}")
    
    async def _demo_intent_recognition(self):
        """Demo intent recognition"""
        commands = [
            "What time is it?",
            "Tell me the current time",
            "I need to know what time it is",
            "Time please"
        ]
        
        print("   Different ways to ask for time:")
        for cmd in commands:
            print(f"   👤 {cmd}")
            response = await self.jarvis.process_text_input(cmd)
            print(f"   🤖 {response}")
    
    async def _demo_task_execution(self):
        """Demo task execution"""
        commands = [
            "Search for machine learning",
            "Open notepad",
            "What's the weather like?"
        ]
        
        for cmd in commands:
            print(f"   👤 {cmd}")
            response = await self.jarvis.process_text_input(cmd)
            print(f"   🤖 {response}")
    
    async def show_system_status(self):
        """Show system status"""
        print("\n📊 System Status")
        print("="*30)
        
        try:
            status = await self.jarvis.get_system_status()
            
            print(f"Running: {status.get('running', 'Unknown')}")
            print(f"Initialized: {status.get('initialized', 'Unknown')}")
            
            components = status.get('components', {})
            print("\nComponents:")
            for name, comp_status in components.items():
                if isinstance(comp_status, dict):
                    initialized = comp_status.get('initialized', 'Unknown')
                    print(f"  {name}: {initialized}")
                else:
                    print(f"  {name}: {comp_status}")
                    
        except Exception as e:
            print(f"Error getting status: {e}")
    
    async def cleanup(self):
        """Cleanup demo resources"""
        if self.jarvis:
            await self.jarvis.shutdown()

def print_welcome():
    """Print welcome message"""
    welcome = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    Enhanced JARVIS                           ║
    ║                      Demo Script                             ║
    ║                                                              ║
    ║  Experience the power of intelligent AI assistance!         ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(welcome)

async def main():
    """Main demo function"""
    print_welcome()
    
    demo = JARVISDemo()
    
    try:
        # Initialize JARVIS
        if not await demo.initialize():
            print("❌ Failed to initialize JARVIS. Exiting.")
            return
        
        # Show system status
        await demo.show_system_status()
        
        # Ask user what they want to do
        print("\n🎯 What would you like to do?")
        print("1. Interactive text demo")
        print("2. Automated demo")
        print("3. Feature demonstration")
        print("4. All of the above")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == "1":
            await demo.run_text_demo()
        elif choice == "2":
            await demo.run_automated_demo()
        elif choice == "3":
            await demo.demonstrate_features()
        elif choice == "4":
            await demo.run_automated_demo()
            await demo.demonstrate_features()
            await demo.run_text_demo()
        else:
            print("Invalid choice. Running interactive demo...")
            await demo.run_text_demo()
    
    except KeyboardInterrupt:
        print("\n\n👋 Demo interrupted. Goodbye!")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
    finally:
        await demo.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
