"""
Task Executor for Enhanced JARVIS
Executes various tasks and automation commands
"""

import asyncio
import logging
import subprocess
import webbrowser
import os
import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import psutil
import requests

@dataclass
class TaskResult:
    """Result of task execution"""
    success: bool
    message: str
    data: Any = None
    error: Optional[str] = None

class TaskExecutor:
    """
    Executes various automation tasks for JARVIS
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Configuration
        self.max_concurrent_tasks = config.get('max_concurrent_tasks', 5)
        self.task_timeout = config.get('timeout', 30)
        
        # Task handlers
        self.task_handlers = self._register_task_handlers()
        
        # TTS engine reference (set externally)
        self.tts_engine = None
        
        # State
        self.is_initialized = False
        self.running_tasks = set()
    
    async def initialize(self):
        """Initialize the task executor"""
        try:
            self.logger.info("Initializing Task Executor...")
            
            # Verify system capabilities
            await self._verify_system_capabilities()
            
            self.is_initialized = True
            self.logger.info("Task Executor initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Task Executor: {e}")
            raise
    
    def _register_task_handlers(self) -> Dict[str, Any]:
        """Register task handlers for different action types"""
        return {
            'speak': self._handle_speak,
            'system': self._handle_system,
            'web': self._handle_web,
            'media': self._handle_media,
            'communication': self._handle_communication,
            'memory': self._handle_memory,
            'get_time': self._handle_get_time,
            'get_date': self._handle_get_date,
            'get_weather': self._handle_get_weather
        }
    
    async def _verify_system_capabilities(self):
        """Verify system capabilities for task execution"""
        # Check if we can access system functions
        try:
            # Test subprocess access
            result = subprocess.run(['echo', 'test'], capture_output=True, text=True, timeout=5)
            if result.returncode != 0:
                self.logger.warning("Limited subprocess access")
        except Exception as e:
            self.logger.warning(f"Subprocess verification failed: {e}")
    
    def set_tts_engine(self, tts_engine):
        """Set reference to TTS engine"""
        self.tts_engine = tts_engine
    
    async def execute_actions(self, actions: List[Any]) -> List[TaskResult]:
        """Execute a list of actions"""
        results = []
        
        try:
            # Execute actions in order
            for action in actions:
                if len(self.running_tasks) >= self.max_concurrent_tasks:
                    # Wait for some tasks to complete
                    await asyncio.sleep(0.1)
                
                result = await self.execute_action(action)
                results.append(result)
                
                # If action failed and was critical, stop execution
                if not result.success and action.priority == 1:
                    self.logger.warning(f"Critical action failed: {action.action_type}")
                    break
            
        except Exception as e:
            self.logger.error(f"Error executing actions: {e}")
            results.append(TaskResult(
                success=False,
                message="Failed to execute actions",
                error=str(e)
            ))
        
        return results
    
    async def execute_action(self, action) -> TaskResult:
        """Execute a single action"""
        try:
            action_type = action.action_type
            parameters = action.parameters
            
            # Get handler for this action type
            handler = self.task_handlers.get(action_type)
            if not handler:
                return TaskResult(
                    success=False,
                    message=f"Unknown action type: {action_type}",
                    error=f"No handler for {action_type}"
                )
            
            # Execute with timeout
            task_id = id(action)
            self.running_tasks.add(task_id)
            
            try:
                result = await asyncio.wait_for(
                    handler(parameters),
                    timeout=self.task_timeout
                )
                return result
            finally:
                self.running_tasks.discard(task_id)
            
        except asyncio.TimeoutError:
            return TaskResult(
                success=False,
                message=f"Action {action.action_type} timed out",
                error="Timeout"
            )
        except Exception as e:
            self.logger.error(f"Error executing action {action.action_type}: {e}")
            return TaskResult(
                success=False,
                message=f"Failed to execute {action.action_type}",
                error=str(e)
            )
    
    async def _handle_speak(self, parameters: Dict[str, Any]) -> TaskResult:
        """Handle speak actions"""
        try:
            # This is handled by the main application, just return success
            return TaskResult(
                success=True,
                message="Speech handled by TTS engine"
            )
        except Exception as e:
            return TaskResult(success=False, message="Speech failed", error=str(e))
    
    async def _handle_system(self, parameters: Dict[str, Any]) -> TaskResult:
        """Handle system actions"""
        try:
            system_action = parameters.get('action')
            
            if system_action == 'shutdown':
                return await self._system_shutdown()
            elif system_action == 'restart':
                return await self._system_restart()
            elif system_action == 'open_application':
                app_name = parameters.get('application', '')
                return await self._open_application(app_name)
            elif system_action == 'close_application':
                app_name = parameters.get('application', '')
                return await self._close_application(app_name)
            else:
                return TaskResult(
                    success=False,
                    message=f"Unknown system action: {system_action}"
                )
                
        except Exception as e:
            return TaskResult(success=False, message="System action failed", error=str(e))
    
    async def _system_shutdown(self) -> TaskResult:
        """Shutdown the system"""
        try:
            if os.name == 'nt':  # Windows
                subprocess.Popen(['shutdown', '/s', '/t', '10'])
            else:  # Unix/Linux
                subprocess.Popen(['sudo', 'shutdown', '-h', '+1'])
            
            return TaskResult(
                success=True,
                message="System shutdown initiated"
            )
        except Exception as e:
            return TaskResult(success=False, message="Shutdown failed", error=str(e))
    
    async def _system_restart(self) -> TaskResult:
        """Restart the system"""
        try:
            if os.name == 'nt':  # Windows
                subprocess.Popen(['shutdown', '/r', '/t', '10'])
            else:  # Unix/Linux
                subprocess.Popen(['sudo', 'reboot'])
            
            return TaskResult(
                success=True,
                message="System restart initiated"
            )
        except Exception as e:
            return TaskResult(success=False, message="Restart failed", error=str(e))
    
    async def _open_application(self, app_name: str) -> TaskResult:
        """Open an application"""
        try:
            app_name = app_name.lower().strip()
            
            # Application mappings
            app_mappings = {
                'chrome': ['chrome', 'google-chrome', 'chrome.exe'],
                'firefox': ['firefox', 'firefox.exe'],
                'notepad': ['notepad', 'notepad.exe'],
                'calculator': ['calc', 'calc.exe', 'calculator'],
                'spotify': ['spotify', 'spotify.exe'],
                'discord': ['discord', 'discord.exe'],
                'teams': ['teams', 'teams.exe'],
                'code': ['code', 'code.exe'],
                'vscode': ['code', 'code.exe']
            }
            
            executables = app_mappings.get(app_name, [app_name])
            
            for executable in executables:
                try:
                    if os.name == 'nt':  # Windows
                        subprocess.Popen([executable], shell=True)
                    else:  # Unix/Linux
                        subprocess.Popen([executable])
                    
                    return TaskResult(
                        success=True,
                        message=f"Opened {app_name}"
                    )
                except FileNotFoundError:
                    continue
            
            return TaskResult(
                success=False,
                message=f"Could not find application: {app_name}"
            )
            
        except Exception as e:
            return TaskResult(success=False, message="Failed to open application", error=str(e))
    
    async def _close_application(self, app_name: str) -> TaskResult:
        """Close an application"""
        try:
            app_name = app_name.lower().strip()
            
            # Find and terminate processes
            terminated = False
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if app_name in proc.info['name'].lower():
                        proc.terminate()
                        terminated = True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            if terminated:
                return TaskResult(
                    success=True,
                    message=f"Closed {app_name}"
                )
            else:
                return TaskResult(
                    success=False,
                    message=f"Could not find running application: {app_name}"
                )
                
        except Exception as e:
            return TaskResult(success=False, message="Failed to close application", error=str(e))
    
    async def _handle_web(self, parameters: Dict[str, Any]) -> TaskResult:
        """Handle web actions"""
        try:
            web_action = parameters.get('action')
            query = parameters.get('query', '')
            
            if web_action == 'search':
                return await self._web_search(query)
            elif web_action == 'youtube_search':
                return await self._youtube_search(query)
            else:
                return TaskResult(
                    success=False,
                    message=f"Unknown web action: {web_action}"
                )
                
        except Exception as e:
            return TaskResult(success=False, message="Web action failed", error=str(e))
    
    async def _web_search(self, query: str) -> TaskResult:
        """Perform web search"""
        try:
            search_url = f"https://www.google.com/search?q={query.replace(' ', '+')}"
            webbrowser.open(search_url)
            
            return TaskResult(
                success=True,
                message=f"Searching for: {query}"
            )
        except Exception as e:
            return TaskResult(success=False, message="Web search failed", error=str(e))
    
    async def _youtube_search(self, query: str) -> TaskResult:
        """Search YouTube"""
        try:
            search_url = f"https://www.youtube.com/results?search_query={query.replace(' ', '+')}"
            webbrowser.open(search_url)
            
            return TaskResult(
                success=True,
                message=f"Searching YouTube for: {query}"
            )
        except Exception as e:
            return TaskResult(success=False, message="YouTube search failed", error=str(e))
    
    async def _handle_media(self, parameters: Dict[str, Any]) -> TaskResult:
        """Handle media actions"""
        try:
            media_action = parameters.get('action')
            
            if media_action == 'play':
                return await self._media_play()
            elif media_action == 'stop':
                return await self._media_stop()
            elif media_action == 'pause':
                return await self._media_pause()
            else:
                return TaskResult(
                    success=False,
                    message=f"Unknown media action: {media_action}"
                )
                
        except Exception as e:
            return TaskResult(success=False, message="Media action failed", error=str(e))
    
    async def _media_play(self) -> TaskResult:
        """Play media"""
        # This would integrate with media players
        return TaskResult(success=True, message="Media playback started")
    
    async def _media_stop(self) -> TaskResult:
        """Stop media"""
        # This would integrate with media players
        return TaskResult(success=True, message="Media playback stopped")
    
    async def _media_pause(self) -> TaskResult:
        """Pause media"""
        # This would integrate with media players
        return TaskResult(success=True, message="Media playback paused")
    
    async def _handle_communication(self, parameters: Dict[str, Any]) -> TaskResult:
        """Handle communication actions"""
        try:
            comm_action = parameters.get('action')
            
            if comm_action == 'send_message':
                contact = parameters.get('contact', '')
                message = parameters.get('message', '')
                return await self._send_message(contact, message)
            elif comm_action == 'make_call':
                contact = parameters.get('contact', '')
                return await self._make_call(contact)
            else:
                return TaskResult(
                    success=False,
                    message=f"Unknown communication action: {comm_action}"
                )
                
        except Exception as e:
            return TaskResult(success=False, message="Communication action failed", error=str(e))
    
    async def _send_message(self, contact: str, message: str) -> TaskResult:
        """Send message (placeholder)"""
        # This would integrate with messaging services
        return TaskResult(
            success=True,
            message=f"Message sent to {contact}: {message}"
        )
    
    async def _make_call(self, contact: str) -> TaskResult:
        """Make call (placeholder)"""
        # This would integrate with calling services
        return TaskResult(
            success=True,
            message=f"Calling {contact}"
        )
    
    async def _handle_memory(self, parameters: Dict[str, Any]) -> TaskResult:
        """Handle memory actions"""
        try:
            memory_action = parameters.get('action')
            
            if memory_action == 'store':
                content = parameters.get('content', '')
                return TaskResult(
                    success=True,
                    message=f"Stored in memory: {content}"
                )
            elif memory_action == 'retrieve':
                query = parameters.get('query', '')
                return TaskResult(
                    success=True,
                    message=f"Retrieved from memory for: {query}"
                )
            else:
                return TaskResult(
                    success=False,
                    message=f"Unknown memory action: {memory_action}"
                )
                
        except Exception as e:
            return TaskResult(success=False, message="Memory action failed", error=str(e))
    
    async def _handle_get_time(self, parameters: Dict[str, Any]) -> TaskResult:
        """Get current time"""
        try:
            current_time = datetime.datetime.now().strftime("%I:%M %p")
            return TaskResult(
                success=True,
                message=f"Current time: {current_time}",
                data={'time': current_time}
            )
        except Exception as e:
            return TaskResult(success=False, message="Failed to get time", error=str(e))
    
    async def _handle_get_date(self, parameters: Dict[str, Any]) -> TaskResult:
        """Get current date"""
        try:
            current_date = datetime.datetime.now().strftime("%A, %B %d, %Y")
            return TaskResult(
                success=True,
                message=f"Current date: {current_date}",
                data={'date': current_date}
            )
        except Exception as e:
            return TaskResult(success=False, message="Failed to get date", error=str(e))
    
    async def _handle_get_weather(self, parameters: Dict[str, Any]) -> TaskResult:
        """Get weather information (placeholder)"""
        try:
            # This would integrate with weather APIs
            location = parameters.get('location', 'your location')
            weather_info = "partly cloudy, 72°F"  # Placeholder
            
            return TaskResult(
                success=True,
                message=f"Weather in {location}: {weather_info}",
                data={'weather': weather_info, 'location': location}
            )
        except Exception as e:
            return TaskResult(success=False, message="Failed to get weather", error=str(e))
    
    async def get_status(self) -> Dict[str, Any]:
        """Get task executor status"""
        return {
            'initialized': self.is_initialized,
            'running_tasks': len(self.running_tasks),
            'max_concurrent_tasks': self.max_concurrent_tasks,
            'available_handlers': list(self.task_handlers.keys())
        }
    
    async def shutdown(self):
        """Shutdown the task executor"""
        try:
            # Wait for running tasks to complete
            while self.running_tasks:
                await asyncio.sleep(0.1)
            
            self.is_initialized = False
            self.logger.info("Task Executor shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during Task Executor shutdown: {e}")
