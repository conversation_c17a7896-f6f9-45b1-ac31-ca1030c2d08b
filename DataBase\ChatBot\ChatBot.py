import random

Hello = ('hello','hey','hii','hi')

reply_Hello = ('Hello <PERSON> .',
            "Hey , What's Up ?",
            "Hey How Are You ?",
            "Hello <PERSON> , Nice To Meet You Again .",
            "Of <PERSON> Jay , Hello .")

Bye = ('bye','exit','go')

reply_bye = ('Bye <PERSON>.',
            "It Will Be Nice To Meet You .",
            "<PERSON> Jay",
            "You Can Call Me Any Time")

How_Are_You = ('how are you','are you fine')

reply_how = ('I Am Fine.',
            "Excellent .",
            "Absolutely Fine.",
            "I'm Fine.",
            "Thanks For Asking.")

thanks = ('nice','good','thanks')

reply_thanks = ('Thanks .',
            "Ohh , It's Okay .",
            "Thanks To <PERSON>.")

Functions = ['functions','abilities','what can you do','features']

reply_Functions = ('I Can Perform Many Task Or Varieties Of Tasks , How Can I Help You ?',
            'I Can Call Your G.F .',
            'I Can Message Your Mom That You Are Not Studing..',
            'I Can Tell Your Class Teacher That You Had Attended All The Online Classes On Insta , <PERSON><PERSON><PERSON> etc!',
            'Let Me Ask You First , How Can I Help You ?',
            'If You Want Me To Tell My Features , Call : Print Features !')

sorry_reply = ("Sorry , That's Beyond My Abilities .",
                "Sorry , I Can't Do That .",
                "Sorry , That's Above Me.")

def ChatterBot(Text):

    Text = str(Text)

    for word in Text.split():

        if word in Hello:

            reply = random.choice(reply_Hello)

            return reply

        elif word in Bye:

            reply = random.choice(reply_bye)

            return reply

        elif word in How_Are_You:

            reply_ = random.choice(reply_how)

            return reply_

        elif word in thanks:

            reply_ = random.choice(reply_thanks)

            return  reply_

        elif word in Functions:

            reply___ = random.choice(reply_Functions)

            return reply___

        else:

            return random.choice(sorry_reply)

