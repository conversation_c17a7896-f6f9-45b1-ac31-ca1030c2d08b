"""
Enhanced Text-to-Speech for JARVIS
High-quality voice synthesis with emotion and personality
"""

import asyncio
import logging
import threading
import queue
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import io
import wave

# TTS libraries
import pyttsx3
import pygame
try:
    from gtts import gTTS
    GTTS_AVAILABLE = True
except ImportError:
    GTTS_AVAILABLE = False
import tempfile
import os

@dataclass
class SpeechRequest:
    """Speech synthesis request"""
    text: str
    emotion: str = "neutral"
    priority: int = 1
    voice_id: Optional[str] = None
    speed: float = 1.0
    volume: float = 1.0
    metadata: Dict[str, Any] = None

@dataclass
class SpeechResult:
    """Speech synthesis result"""
    success: bool
    duration: float
    audio_file: Optional[str] = None
    error: Optional[str] = None

class VoiceProfile:
    """Voice profile configuration"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.voice_id = config.get('voice_id', 0)
        self.rate = config.get('rate', 150)
        self.volume = config.get('volume', 0.9)
        self.pitch = config.get('pitch', 0)
        self.emotion_modifiers = config.get('emotion_modifiers', {})

class EnhancedTextToSpeech:
    """
    Enhanced Text-to-Speech with emotion and personality
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # TTS configuration
        self.default_engine = config.get('engine', 'pyttsx3')  # pyttsx3, gtts
        self.voice_profiles = self._load_voice_profiles(config.get('voice_profiles', {}))
        self.current_profile = config.get('default_profile', 'default')
        
        # Audio configuration
        self.audio_format = config.get('audio_format', 'wav')
        self.sample_rate = config.get('sample_rate', 22050)
        
        # TTS engines
        self.pyttsx3_engine = None
        self.pygame_mixer = None
        
        # Speech queue for managing multiple requests
        self.speech_queue = queue.PriorityQueue()
        self.is_speaking = False
        self.speech_thread = None
        
        # State
        self.is_initialized = False
        self.muted = False
    
    def _load_voice_profiles(self, profiles_config: Dict[str, Any]) -> Dict[str, VoiceProfile]:
        """Load voice profiles from configuration"""
        profiles = {}
        
        # Default profile
        profiles['default'] = VoiceProfile('default', {
            'voice_id': 0,
            'rate': 150,
            'volume': 0.9,
            'emotion_modifiers': {
                'happy': {'rate': 160, 'pitch': 10},
                'sad': {'rate': 130, 'pitch': -10},
                'excited': {'rate': 180, 'pitch': 20},
                'calm': {'rate': 140, 'pitch': -5},
                'angry': {'rate': 170, 'pitch': 15},
                'whisper': {'rate': 120, 'volume': 0.5}
            }
        })
        
        # Load custom profiles
        for name, config in profiles_config.items():
            profiles[name] = VoiceProfile(name, config)
        
        return profiles
    
    async def initialize(self):
        """Initialize the TTS system"""
        try:
            self.logger.info("Initializing Enhanced Text-to-Speech...")
            
            # Initialize pyttsx3 engine
            self.pyttsx3_engine = pyttsx3.init()
            
            # Configure default voice
            await self._configure_voice()
            
            # Initialize pygame mixer for audio playback
            pygame.mixer.init(frequency=self.sample_rate, size=-16, channels=2, buffer=1024)
            
            # Start speech processing thread
            self.speech_thread = threading.Thread(target=self._speech_worker)
            self.speech_thread.daemon = True
            self.speech_thread.start()
            
            self.is_initialized = True
            self.logger.info("Text-to-Speech initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Text-to-Speech: {e}")
            raise
    
    async def _configure_voice(self):
        """Configure voice settings"""
        try:
            if not self.pyttsx3_engine:
                return
            
            # Get current profile
            profile = self.voice_profiles.get(self.current_profile, self.voice_profiles['default'])
            
            # Set voice
            voices = self.pyttsx3_engine.getProperty('voices')
            if voices and len(voices) > profile.voice_id:
                self.pyttsx3_engine.setProperty('voice', voices[profile.voice_id].id)
            
            # Set rate and volume
            self.pyttsx3_engine.setProperty('rate', profile.rate)
            self.pyttsx3_engine.setProperty('volume', profile.volume)
            
            self.logger.debug(f"Configured voice profile: {profile.name}")
            
        except Exception as e:
            self.logger.error(f"Error configuring voice: {e}")
    
    async def speak(self, 
                   text: str, 
                   emotion: str = "neutral",
                   priority: int = 1,
                   wait: bool = False) -> SpeechResult:
        """
        Speak text with specified emotion and priority
        
        Args:
            text: Text to speak
            emotion: Emotional tone (neutral, happy, sad, excited, etc.)
            priority: Priority level (1 = highest)
            wait: Whether to wait for speech to complete
            
        Returns:
            SpeechResult with success status and metadata
        """
        if not self.is_initialized:
            await self.initialize()
        
        if self.muted:
            return SpeechResult(success=False, duration=0.0, error="TTS is muted")
        
        try:
            # Create speech request
            request = SpeechRequest(
                text=text.strip(),
                emotion=emotion,
                priority=priority,
                voice_id=self.current_profile,
                metadata={'timestamp': datetime.now().isoformat()}
            )
            
            # Add to queue
            self.speech_queue.put((priority, datetime.now(), request))
            
            if wait:
                # Wait for speech to complete (simplified implementation)
                while not self.speech_queue.empty() or self.is_speaking:
                    await asyncio.sleep(0.1)
            
            self.logger.debug(f"Queued speech: {text[:50]}...")
            return SpeechResult(success=True, duration=0.0)
            
        except Exception as e:
            self.logger.error(f"Error in speak: {e}")
            return SpeechResult(success=False, duration=0.0, error=str(e))
    
    def _speech_worker(self):
        """Background worker for processing speech queue"""
        while True:
            try:
                # Get next speech request
                priority, timestamp, request = self.speech_queue.get(timeout=1)
                
                # Process speech request
                self._process_speech_request(request)
                
                # Mark task as done
                self.speech_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"Error in speech worker: {e}")
    
    def _process_speech_request(self, request: SpeechRequest):
        """Process individual speech request"""
        try:
            self.is_speaking = True
            
            # Apply emotion modifiers
            self._apply_emotion_modifiers(request.emotion)
            
            # Choose synthesis method
            if self.default_engine == 'pyttsx3':
                self._speak_with_pyttsx3(request.text)
            elif self.default_engine == 'gtts':
                self._speak_with_gtts(request.text)
            else:
                self._speak_with_pyttsx3(request.text)  # Fallback
            
            self.logger.debug(f"Spoke: {request.text[:50]}...")
            
        except Exception as e:
            self.logger.error(f"Error processing speech request: {e}")
        finally:
            self.is_speaking = False
    
    def _apply_emotion_modifiers(self, emotion: str):
        """Apply emotion-based voice modifiers"""
        try:
            profile = self.voice_profiles.get(self.current_profile, self.voice_profiles['default'])
            modifiers = profile.emotion_modifiers.get(emotion, {})
            
            if modifiers and self.pyttsx3_engine:
                # Apply rate modifier
                if 'rate' in modifiers:
                    new_rate = modifiers['rate']
                    self.pyttsx3_engine.setProperty('rate', new_rate)
                
                # Apply volume modifier
                if 'volume' in modifiers:
                    new_volume = modifiers['volume']
                    self.pyttsx3_engine.setProperty('volume', new_volume)
                
                # Note: Pitch modification requires more advanced TTS engines
                # pyttsx3 doesn't support pitch control directly
                
        except Exception as e:
            self.logger.error(f"Error applying emotion modifiers: {e}")
    
    def _speak_with_pyttsx3(self, text: str):
        """Speak using pyttsx3 engine"""
        try:
            if self.pyttsx3_engine:
                self.pyttsx3_engine.say(text)
                self.pyttsx3_engine.runAndWait()
        except Exception as e:
            self.logger.error(f"Error with pyttsx3: {e}")
    
    def _speak_with_gtts(self, text: str):
        """Speak using Google Text-to-Speech"""
        if not GTTS_AVAILABLE:
            self.logger.warning("gTTS not available, falling back to pyttsx3")
            self._speak_with_pyttsx3(text)
            return

        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.mp3') as temp_file:
                temp_filename = temp_file.name

            # Generate speech
            tts = gTTS(text=text, lang='en', slow=False)
            tts.save(temp_filename)

            # Play audio
            pygame.mixer.music.load(temp_filename)
            pygame.mixer.music.play()

            # Wait for playback to complete
            while pygame.mixer.music.get_busy():
                pygame.time.wait(100)

            # Clean up
            os.unlink(temp_filename)

        except Exception as e:
            self.logger.error(f"Error with gTTS: {e}")
            # Fallback to pyttsx3
            self._speak_with_pyttsx3(text)
    
    async def speak_ssml(self, ssml: str) -> SpeechResult:
        """Speak SSML (Speech Synthesis Markup Language) text"""
        # For now, strip SSML tags and speak as plain text
        # Future implementation could parse SSML for advanced control
        import re
        plain_text = re.sub(r'<[^>]+>', '', ssml)
        return await self.speak(plain_text)
    
    async def set_voice_profile(self, profile_name: str):
        """Change the current voice profile"""
        if profile_name in self.voice_profiles:
            self.current_profile = profile_name
            await self._configure_voice()
            self.logger.info(f"Changed to voice profile: {profile_name}")
        else:
            self.logger.warning(f"Voice profile not found: {profile_name}")
    
    async def get_available_voices(self) -> List[Dict[str, Any]]:
        """Get list of available voices"""
        voices = []
        try:
            if self.pyttsx3_engine:
                pyttsx3_voices = self.pyttsx3_engine.getProperty('voices')
                for i, voice in enumerate(pyttsx3_voices):
                    voices.append({
                        'id': i,
                        'name': voice.name,
                        'language': getattr(voice, 'languages', ['en']),
                        'gender': getattr(voice, 'gender', 'unknown'),
                        'engine': 'pyttsx3'
                    })
        except Exception as e:
            self.logger.error(f"Error getting voices: {e}")
        
        return voices
    
    async def set_volume(self, volume: float):
        """Set global volume (0.0 to 1.0)"""
        try:
            volume = max(0.0, min(1.0, volume))
            if self.pyttsx3_engine:
                self.pyttsx3_engine.setProperty('volume', volume)
            self.logger.debug(f"Set volume to {volume}")
        except Exception as e:
            self.logger.error(f"Error setting volume: {e}")
    
    async def set_rate(self, rate: int):
        """Set speech rate (words per minute)"""
        try:
            rate = max(50, min(400, rate))  # Reasonable bounds
            if self.pyttsx3_engine:
                self.pyttsx3_engine.setProperty('rate', rate)
            self.logger.debug(f"Set rate to {rate} WPM")
        except Exception as e:
            self.logger.error(f"Error setting rate: {e}")
    
    async def mute(self):
        """Mute text-to-speech"""
        self.muted = True
        self.logger.info("TTS muted")
    
    async def unmute(self):
        """Unmute text-to-speech"""
        self.muted = False
        self.logger.info("TTS unmuted")
    
    async def stop_speaking(self):
        """Stop current speech"""
        try:
            if self.pyttsx3_engine:
                self.pyttsx3_engine.stop()
            
            if pygame.mixer.music.get_busy():
                pygame.mixer.music.stop()
            
            # Clear queue
            while not self.speech_queue.empty():
                try:
                    self.speech_queue.get_nowait()
                except queue.Empty:
                    break
            
            self.is_speaking = False
            self.logger.info("Stopped speaking")
            
        except Exception as e:
            self.logger.error(f"Error stopping speech: {e}")
    
    async def get_status(self) -> Dict[str, Any]:
        """Get TTS status"""
        return {
            'initialized': self.is_initialized,
            'muted': self.muted,
            'is_speaking': self.is_speaking,
            'queue_size': self.speech_queue.qsize(),
            'current_profile': self.current_profile,
            'available_profiles': list(self.voice_profiles.keys()),
            'default_engine': self.default_engine
        }
    
    async def shutdown(self):
        """Shutdown the TTS system"""
        try:
            await self.stop_speaking()
            
            if self.pyttsx3_engine:
                self.pyttsx3_engine.stop()
            
            if pygame.mixer.get_init():
                pygame.mixer.quit()
            
            self.is_initialized = False
            self.logger.info("Text-to-Speech shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during TTS shutdown: {e}")
