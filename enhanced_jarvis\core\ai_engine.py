"""
Enhanced JARVIS AI Engine
Core intelligence module for context-aware processing and decision making
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import json

from .context_manager import ContextManager
from .memory_system import MemorySystem
from .intent_recognition import Inten<PERSON><PERSON><PERSON><PERSON><PERSON>zer
from .response_generator import ResponseGenerator
from .decision_engine import DecisionEngine

@dataclass
class ProcessingResult:
    """Result of AI processing"""
    intent: str
    confidence: float
    context: Dict[str, Any]
    response: str
    actions: List[Dict[str, Any]]
    metadata: Dict[str, Any]

class AIEngine:
    """
    Core AI Engine for Enhanced JARVIS
    Orchestrates all AI components for intelligent processing
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize core components
        self.context_manager = ContextManager(config.get('context', {}))
        self.memory_system = MemorySystem(config.get('memory', {}))
        self.intent_recognizer = IntentRecognizer(config.get('intent', {}))
        self.response_generator = ResponseGenerator(config.get('response', {}))
        self.decision_engine = DecisionEngine(config.get('decision', {}))
        
        # State management
        self.is_initialized = False
        self.session_id = None
        
    async def initialize(self):
        """Initialize all AI components"""
        try:
            self.logger.info("Initializing Enhanced JARVIS AI Engine...")
            
            # Initialize components in order
            await self.memory_system.initialize()
            await self.context_manager.initialize()
            await self.intent_recognizer.initialize()
            await self.response_generator.initialize()
            await self.decision_engine.initialize()
            
            self.is_initialized = True
            self.session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            self.logger.info("AI Engine initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize AI Engine: {e}")
            raise
    
    async def process_input(self, 
                          input_text: str, 
                          input_type: str = "text",
                          user_id: str = "default",
                          metadata: Optional[Dict[str, Any]] = None) -> ProcessingResult:
        """
        Process user input through the AI pipeline
        
        Args:
            input_text: The user's input
            input_type: Type of input (text, voice, etc.)
            user_id: Unique identifier for the user
            metadata: Additional context metadata
            
        Returns:
            ProcessingResult with intent, response, and actions
        """
        if not self.is_initialized:
            await self.initialize()
        
        try:
            # Step 1: Update context with new input
            context = await self.context_manager.update_context(
                input_text, user_id, input_type, metadata or {}
            )
            
            # Step 2: Recognize intent
            intent_result = await self.intent_recognizer.recognize(
                input_text, context
            )
            
            # Step 3: Retrieve relevant memories
            memories = await self.memory_system.retrieve_relevant_memories(
                input_text, intent_result.intent, user_id
            )
            
            # Step 4: Make decision based on intent and context
            decision = await self.decision_engine.make_decision(
                intent_result, context, memories
            )
            
            # Step 5: Generate response
            response = await self.response_generator.generate_response(
                intent_result, decision, context, memories
            )
            
            # Step 6: Store interaction in memory
            await self.memory_system.store_interaction(
                user_id, input_text, response.text, intent_result.intent, context
            )
            
            # Create result
            result = ProcessingResult(
                intent=intent_result.intent,
                confidence=intent_result.confidence,
                context=context,
                response=response.text,
                actions=decision.actions,
                metadata={
                    'session_id': self.session_id,
                    'timestamp': datetime.now().isoformat(),
                    'processing_time': response.processing_time,
                    'user_id': user_id
                }
            )
            
            self.logger.info(f"Processed input successfully: {intent_result.intent}")
            return result
            
        except Exception as e:
            self.logger.error(f"Error processing input: {e}")
            # Return error response
            return ProcessingResult(
                intent="error",
                confidence=0.0,
                context={},
                response="I apologize, but I encountered an error processing your request.",
                actions=[],
                metadata={'error': str(e)}
            )
    
    async def learn_from_feedback(self, 
                                interaction_id: str, 
                                feedback: Dict[str, Any]):
        """Learn from user feedback to improve responses"""
        try:
            await self.memory_system.store_feedback(interaction_id, feedback)
            await self.decision_engine.update_from_feedback(feedback)
            self.logger.info(f"Learned from feedback for interaction: {interaction_id}")
        except Exception as e:
            self.logger.error(f"Error learning from feedback: {e}")
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get current system status and health metrics"""
        return {
            'initialized': self.is_initialized,
            'session_id': self.session_id,
            'components': {
                'context_manager': await self.context_manager.get_status(),
                'memory_system': await self.memory_system.get_status(),
                'intent_recognizer': await self.intent_recognizer.get_status(),
                'response_generator': await self.response_generator.get_status(),
                'decision_engine': await self.decision_engine.get_status()
            }
        }
    
    async def shutdown(self):
        """Gracefully shutdown the AI engine"""
        try:
            self.logger.info("Shutting down AI Engine...")
            
            # Shutdown components in reverse order
            await self.decision_engine.shutdown()
            await self.response_generator.shutdown()
            await self.intent_recognizer.shutdown()
            await self.context_manager.shutdown()
            await self.memory_system.shutdown()
            
            self.is_initialized = False
            self.logger.info("AI Engine shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
