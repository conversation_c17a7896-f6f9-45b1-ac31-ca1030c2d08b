"""
Setup script for Enhanced JARVIS
"""

from setuptools import setup, find_packages
import os

# Read README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="enhanced-jarvis",
    version="1.0.0",
    author="Your Name",
    author_email="<EMAIL>",
    description="Enhanced JARVIS - Intelligent AI Assistant with Context Awareness",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/enhanced-jarvis",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: System :: Systems Administration",
        "Topic :: Multimedia :: Sound/Audio :: Speech",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.19.0",
            "black>=22.0.0",
            "flake8>=4.0.0",
            "mypy>=0.971",
        ],
        "advanced": [
            "torch>=1.12.0",
            "transformers>=4.20.0",
            "sentence-transformers>=2.2.0",
            "opencv-python>=4.6.0",
        ],
        "gui": [
            "PyQt5>=5.15.0",
            "kivy>=2.1.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "enhanced-jarvis=enhanced_jarvis.main:main",
            "jarvis=enhanced_jarvis.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "enhanced_jarvis": [
            "data/*.json",
            "data/*.db",
            "config/*.yaml",
            "config/*.json",
        ],
    },
    zip_safe=False,
)
