# Enhanced JARVIS Requirements

# Core AI and NLP
numpy>=1.21.0
scipy>=1.7.0
scikit-learn>=1.0.0
spacy>=3.4.0

# Speech Recognition and Audio
SpeechRecognition>=3.8.1
pyaudio>=0.2.11
webrtcvad>=2.0.10
pydub>=0.25.1

# Text-to-Speech
pyttsx3>=2.90
gTTS>=2.2.4
pygame>=2.1.0

# System Integration
psutil>=5.8.0
pyautogui>=0.9.53
keyboard>=0.13.5
pygetwindow>=0.0.9

# Web and API Integration
requests>=2.28.0
beautifulsoup4>=4.11.0
selenium>=4.0.0
pywhatkit>=5.4

# Database and Storage
sqlite3  # Built-in with Python
aiosqlite>=0.17.0

# Async and Concurrency
asyncio  # Built-in with Python
aiohttp>=3.8.0
aiofiles>=0.8.0

# Utilities
python-dateutil>=2.8.2
pytz>=2022.1
colorama>=0.4.4
rich>=12.0.0

# Optional: Advanced NLP (uncomment if needed)
# torch>=1.12.0
# transformers>=4.20.0
# sentence-transformers>=2.2.0

# Optional: Computer Vision (uncomment if needed)
# opencv-python>=4.6.0
# Pillow>=9.0.0

# Optional: Home Automation (uncomment if needed)
# paho-mqtt>=1.6.0
# homeassistant>=2022.7.0

# Development and Testing
pytest>=7.0.0
pytest-asyncio>=0.19.0
black>=22.0.0
flake8>=4.0.0

# Logging and Monitoring
loguru>=0.6.0

# Configuration
pyyaml>=6.0
python-dotenv>=0.20.0

# Notifications
plyer>=2.1.0
win10toast>=0.9  # Windows only

# Optional: Voice Activity Detection
# webrtcvad-wheels>=2.0.10  # Alternative if webrtcvad fails

# Optional: Advanced Audio Processing
# librosa>=0.9.0
# soundfile>=0.10.0

# Optional: GUI Framework (if building a GUI)
# tkinter  # Built-in with Python
# PyQt5>=5.15.0
# kivy>=2.1.0
