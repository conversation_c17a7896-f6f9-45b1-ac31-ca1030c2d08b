"""
Intent Recognition for Enhanced JARVIS
Advanced NLP-based intent classification and entity extraction
"""

import asyncio
import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import json

# For now, we'll use pattern-based recognition
# This can be enhanced with transformers/BERT later
try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    import numpy as np
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

@dataclass
class IntentResult:
    """Result of intent recognition"""
    intent: str
    confidence: float
    entities: Dict[str, Any]
    alternative_intents: List[Tuple[str, float]]
    processing_time: float

@dataclass
class IntentPattern:
    """Intent pattern definition"""
    intent: str
    patterns: List[str]
    entities: List[str]
    examples: List[str]
    priority: int = 1

class IntentRecognizer:
    """
    Advanced intent recognition using multiple approaches
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # NLP components
        self.nlp = None
        self.vectorizer = None
        self.intent_vectors = None
        
        # Intent definitions
        self.intent_patterns = self._load_intent_patterns()
        self.entity_extractors = self._load_entity_extractors()
        
        # State
        self.is_initialized = False
    
    async def initialize(self):
        """Initialize the intent recognizer"""
        try:
            self.logger.info("Initializing Intent Recognizer...")
            
            # Load spaCy model if available
            if SPACY_AVAILABLE:
                try:
                    self.nlp = spacy.load("en_core_web_sm")
                except OSError:
                    self.logger.warning("spaCy model not found, using basic processing")
                    self.nlp = None
            else:
                self.logger.info("spaCy not available, using basic processing")
                self.nlp = None
            
            # Initialize vectorizer with intent examples
            await self._initialize_vectorizer()
            
            self.is_initialized = True
            self.logger.info("Intent Recognizer initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Intent Recognizer: {e}")
            raise
    
    def _load_intent_patterns(self) -> Dict[str, IntentPattern]:
        """Load intent patterns and examples"""
        patterns = {
            # Greeting and conversation
            'greeting': IntentPattern(
                intent='greeting',
                patterns=[
                    r'\b(hello|hi|hey|good morning|good afternoon|good evening)\b',
                    r'\bjarvis\b.*\b(hello|hi|hey)\b'
                ],
                entities=[],
                examples=[
                    "hello jarvis", "hi there", "good morning", "hey jarvis"
                ]
            ),
            
            # Questions and information
            'question_time': IntentPattern(
                intent='question_time',
                patterns=[
                    r'\b(what time|what\'s the time|current time|time is)\b',
                    r'\btell me.*time\b'
                ],
                entities=[],
                examples=[
                    "what time is it", "tell me the time", "current time"
                ]
            ),
            
            'question_date': IntentPattern(
                intent='question_date',
                patterns=[
                    r'\b(what date|what\'s the date|today\'s date|current date)\b',
                    r'\btell me.*date\b'
                ],
                entities=[],
                examples=[
                    "what's the date", "tell me today's date", "current date"
                ]
            ),
            
            'question_weather': IntentPattern(
                intent='question_weather',
                patterns=[
                    r'\b(weather|temperature|forecast)\b',
                    r'\bhow.*weather\b',
                    r'\bwhat.*weather\b'
                ],
                entities=['location'],
                examples=[
                    "what's the weather", "weather forecast", "temperature outside"
                ]
            ),
            
            # System control
            'system_shutdown': IntentPattern(
                intent='system_shutdown',
                patterns=[
                    r'\b(shutdown|turn off|power off)\b',
                    r'\bshut down.*system\b'
                ],
                entities=[],
                examples=[
                    "shutdown the system", "turn off computer", "power off"
                ]
            ),
            
            'system_restart': IntentPattern(
                intent='system_restart',
                patterns=[
                    r'\b(restart|reboot)\b',
                    r'\brestart.*system\b'
                ],
                entities=[],
                examples=[
                    "restart the system", "reboot computer"
                ]
            ),
            
            # Application control
            'open_application': IntentPattern(
                intent='open_application',
                patterns=[
                    r'\bopen\s+(\w+)\b',
                    r'\bstart\s+(\w+)\b',
                    r'\blaunch\s+(\w+)\b'
                ],
                entities=['application'],
                examples=[
                    "open chrome", "start notepad", "launch spotify"
                ]
            ),
            
            'close_application': IntentPattern(
                intent='close_application',
                patterns=[
                    r'\bclose\s+(\w+)\b',
                    r'\bshut\s+(\w+)\b',
                    r'\bexit\s+(\w+)\b'
                ],
                entities=['application'],
                examples=[
                    "close chrome", "shut notepad", "exit spotify"
                ]
            ),
            
            # Media control
            'play_music': IntentPattern(
                intent='play_music',
                patterns=[
                    r'\bplay\s+(music|song|audio)\b',
                    r'\bstart.*music\b',
                    r'\bplay\s+(.+)\b'
                ],
                entities=['media_name'],
                examples=[
                    "play music", "play some jazz", "start playing music"
                ]
            ),
            
            'stop_music': IntentPattern(
                intent='stop_music',
                patterns=[
                    r'\bstop\s+(music|song|audio|playing)\b',
                    r'\bpause.*music\b'
                ],
                entities=[],
                examples=[
                    "stop music", "pause the song", "stop playing"
                ]
            ),
            
            # Search and information
            'search_web': IntentPattern(
                intent='search_web',
                patterns=[
                    r'\bsearch\s+(.+)\b',
                    r'\bgoogle\s+(.+)\b',
                    r'\blook up\s+(.+)\b'
                ],
                entities=['query'],
                examples=[
                    "search for python tutorials", "google machine learning", "look up weather"
                ]
            ),
            
            'search_youtube': IntentPattern(
                intent='search_youtube',
                patterns=[
                    r'\byoutube\s+(.+)\b',
                    r'\bplay.*youtube\s+(.+)\b',
                    r'\bwatch\s+(.+)\b'
                ],
                entities=['query'],
                examples=[
                    "youtube search music", "play on youtube", "watch funny videos"
                ]
            ),
            
            # Communication
            'send_message': IntentPattern(
                intent='send_message',
                patterns=[
                    r'\bsend.*message\b',
                    r'\btext\s+(.+)\b',
                    r'\bmessage\s+(.+)\b'
                ],
                entities=['contact', 'message'],
                examples=[
                    "send message to john", "text mom", "message my friend"
                ]
            ),
            
            'make_call': IntentPattern(
                intent='make_call',
                patterns=[
                    r'\bcall\s+(.+)\b',
                    r'\bphone\s+(.+)\b',
                    r'\bdial\s+(.+)\b'
                ],
                entities=['contact'],
                examples=[
                    "call john", "phone my mom", "dial emergency"
                ]
            ),
            
            # Memory and notes
            'remember_something': IntentPattern(
                intent='remember_something',
                patterns=[
                    r'\bremember\s+(.+)\b',
                    r'\bnote\s+(.+)\b',
                    r'\bsave.*note\b'
                ],
                entities=['content'],
                examples=[
                    "remember to buy milk", "note that meeting is at 3pm", "save this note"
                ]
            ),
            
            'recall_memory': IntentPattern(
                intent='recall_memory',
                patterns=[
                    r'\bwhat.*remember\b',
                    r'\brecall\b',
                    r'\bdo you remember\b'
                ],
                entities=['query'],
                examples=[
                    "what do you remember", "recall my notes", "do you remember what I said"
                ]
            ),
            
            # General conversation
            'how_are_you': IntentPattern(
                intent='how_are_you',
                patterns=[
                    r'\bhow are you\b',
                    r'\bhow.*doing\b',
                    r'\bwhat\'s up\b'
                ],
                entities=[],
                examples=[
                    "how are you", "how are you doing", "what's up"
                ]
            ),
            
            'goodbye': IntentPattern(
                intent='goodbye',
                patterns=[
                    r'\b(bye|goodbye|see you|farewell)\b',
                    r'\bgood night\b',
                    r'\btalk.*later\b'
                ],
                entities=[],
                examples=[
                    "goodbye", "bye jarvis", "good night", "see you later"
                ]
            ),
            
            # Help and information about JARVIS
            'help': IntentPattern(
                intent='help',
                patterns=[
                    r'\bhelp\b',
                    r'\bwhat.*can.*do\b',
                    r'\bcommands\b'
                ],
                entities=[],
                examples=[
                    "help me", "what can you do", "show me commands"
                ]
            ),
            
            'who_are_you': IntentPattern(
                intent='who_are_you',
                patterns=[
                    r'\bwho are you\b',
                    r'\bwhat.*your name\b',
                    r'\bintroduce yourself\b'
                ],
                entities=[],
                examples=[
                    "who are you", "what's your name", "introduce yourself"
                ]
            )
        }
        
        return patterns
    
    def _load_entity_extractors(self) -> Dict[str, Any]:
        """Load entity extraction patterns"""
        return {
            'application': {
                'patterns': [
                    r'\b(chrome|firefox|notepad|calculator|spotify|discord|teams)\b'
                ],
                'aliases': {
                    'browser': 'chrome',
                    'text editor': 'notepad',
                    'calc': 'calculator'
                }
            },
            'contact': {
                'patterns': [
                    r'\b([A-Z][a-z]+)\b',  # Proper names
                    r'\b(mom|dad|mother|father|friend|boss)\b'
                ]
            },
            'location': {
                'patterns': [
                    r'\bin\s+([A-Z][a-z]+)\b',
                    r'\bat\s+([A-Z][a-z]+)\b',
                    r'\b(here|outside|home|work|office)\b'
                ]
            }
        }
    
    async def _initialize_vectorizer(self):
        """Initialize TF-IDF vectorizer with intent examples"""
        try:
            # Collect all examples
            all_examples = []
            intent_labels = []
            
            for intent_name, pattern in self.intent_patterns.items():
                for example in pattern.examples:
                    all_examples.append(example.lower())
                    intent_labels.append(intent_name)
            
            if all_examples and SKLEARN_AVAILABLE:
                self.vectorizer = TfidfVectorizer(
                    ngram_range=(1, 2),
                    stop_words='english',
                    max_features=1000
                )

                self.intent_vectors = self.vectorizer.fit_transform(all_examples)
                self.intent_labels = intent_labels

                self.logger.info(f"Initialized vectorizer with {len(all_examples)} examples")
            else:
                self.logger.info("Sklearn not available, using pattern-based recognition only")
            
        except Exception as e:
            self.logger.error(f"Error initializing vectorizer: {e}")
    
    async def recognize(self, text: str, context: Dict[str, Any] = None) -> IntentResult:
        """
        Recognize intent from input text
        
        Args:
            text: Input text to analyze
            context: Additional context information
            
        Returns:
            IntentResult with recognized intent and entities
        """
        start_time = datetime.now()
        
        try:
            text_lower = text.lower().strip()
            
            # Method 1: Pattern-based recognition
            pattern_result = await self._pattern_based_recognition(text_lower)
            
            # Method 2: Vector similarity (if available)
            vector_result = await self._vector_based_recognition(text_lower)
            
            # Method 3: Context-based adjustment
            context_result = await self._context_based_adjustment(
                pattern_result, vector_result, context or {}
            )
            
            # Extract entities
            entities = await self._extract_entities(text, context_result['intent'])
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = IntentResult(
                intent=context_result['intent'],
                confidence=context_result['confidence'],
                entities=entities,
                alternative_intents=context_result.get('alternatives', []),
                processing_time=processing_time
            )
            
            self.logger.debug(f"Recognized intent: {result.intent} (confidence: {result.confidence:.2f})")
            return result
            
        except Exception as e:
            self.logger.error(f"Error in intent recognition: {e}")
            return IntentResult(
                intent='unknown',
                confidence=0.0,
                entities={},
                alternative_intents=[],
                processing_time=(datetime.now() - start_time).total_seconds()
            )
    
    async def _pattern_based_recognition(self, text: str) -> Dict[str, Any]:
        """Pattern-based intent recognition"""
        best_match = None
        best_score = 0.0
        alternatives = []
        
        for intent_name, pattern in self.intent_patterns.items():
            score = 0.0
            
            # Check regex patterns
            for regex_pattern in pattern.patterns:
                if re.search(regex_pattern, text, re.IGNORECASE):
                    score = max(score, 0.8)
            
            # Check example similarity (simple word overlap)
            for example in pattern.examples:
                example_words = set(example.lower().split())
                text_words = set(text.split())
                overlap = len(example_words.intersection(text_words))
                if overlap > 0:
                    similarity = overlap / max(len(example_words), len(text_words))
                    score = max(score, similarity * 0.6)
            
            if score > 0:
                alternatives.append((intent_name, score))
                if score > best_score:
                    best_score = score
                    best_match = intent_name
        
        # Sort alternatives by score
        alternatives.sort(key=lambda x: x[1], reverse=True)
        
        return {
            'intent': best_match or 'unknown',
            'confidence': best_score,
            'alternatives': alternatives[:3]  # Top 3 alternatives
        }
    
    async def _vector_based_recognition(self, text: str) -> Dict[str, Any]:
        """Vector similarity-based intent recognition"""
        if not SKLEARN_AVAILABLE or not self.vectorizer or not hasattr(self, 'intent_vectors'):
            return {'intent': 'unknown', 'confidence': 0.0, 'alternatives': []}

        try:
            # Transform input text
            text_vector = self.vectorizer.transform([text])

            # Calculate similarities
            similarities = cosine_similarity(text_vector, self.intent_vectors)[0]

            # Find best matches
            best_indices = np.argsort(similarities)[::-1][:5]  # Top 5

            alternatives = []
            for idx in best_indices:
                if similarities[idx] > 0.1:  # Minimum threshold
                    intent = self.intent_labels[idx]
                    score = similarities[idx]
                    alternatives.append((intent, score))

            if alternatives:
                best_intent, best_score = alternatives[0]
                return {
                    'intent': best_intent,
                    'confidence': best_score,
                    'alternatives': alternatives
                }

        except Exception as e:
            self.logger.error(f"Error in vector-based recognition: {e}")

        return {'intent': 'unknown', 'confidence': 0.0, 'alternatives': []}
    
    async def _context_based_adjustment(self, 
                                      pattern_result: Dict[str, Any],
                                      vector_result: Dict[str, Any],
                                      context: Dict[str, Any]) -> Dict[str, Any]:
        """Adjust recognition based on context"""
        # Combine pattern and vector results
        pattern_confidence = pattern_result['confidence']
        vector_confidence = vector_result['confidence']
        
        # Weight pattern matching higher for exact matches
        if pattern_confidence > 0.7:
            final_result = pattern_result
        elif vector_confidence > pattern_confidence:
            final_result = vector_result
        else:
            final_result = pattern_result
        
        # Context-based adjustments
        current_task = context.get('current_task')
        if current_task:
            # Boost confidence for task-related intents
            if current_task in final_result['intent']:
                final_result['confidence'] = min(1.0, final_result['confidence'] * 1.2)
        
        # Time-based adjustments
        temporal_context = context.get('temporal_context', {})
        hour = temporal_context.get('hour', 12)
        
        if hour < 6 or hour > 22:  # Late night/early morning
            if final_result['intent'] in ['goodbye', 'system_shutdown']:
                final_result['confidence'] = min(1.0, final_result['confidence'] * 1.1)
        
        return final_result
    
    async def _extract_entities(self, text: str, intent: str) -> Dict[str, Any]:
        """Extract entities from text based on intent"""
        entities = {}
        
        try:
            # Get entity patterns for this intent
            intent_pattern = self.intent_patterns.get(intent)
            if not intent_pattern:
                return entities
            
            for entity_type in intent_pattern.entities:
                entity_config = self.entity_extractors.get(entity_type, {})
                patterns = entity_config.get('patterns', [])
                
                for pattern in patterns:
                    matches = re.findall(pattern, text, re.IGNORECASE)
                    if matches:
                        if entity_type not in entities:
                            entities[entity_type] = []
                        entities[entity_type].extend(matches)
            
            # Use spaCy for named entity recognition if available
            if self.nlp:
                doc = self.nlp(text)
                for ent in doc.ents:
                    entity_type = ent.label_.lower()
                    if entity_type not in entities:
                        entities[entity_type] = []
                    entities[entity_type].append(ent.text)
            
        except Exception as e:
            self.logger.error(f"Error extracting entities: {e}")
        
        return entities
    
    async def get_status(self) -> Dict[str, Any]:
        """Get intent recognizer status"""
        return {
            'initialized': self.is_initialized,
            'total_intents': len(self.intent_patterns),
            'vectorizer_ready': self.vectorizer is not None,
            'spacy_available': self.nlp is not None
        }
    
    async def shutdown(self):
        """Shutdown the intent recognizer"""
        try:
            self.is_initialized = False
            self.logger.info("Intent Recognizer shutdown complete")
        except Exception as e:
            self.logger.error(f"Error during Intent Recognizer shutdown: {e}")
