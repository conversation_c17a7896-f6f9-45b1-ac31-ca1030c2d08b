import os
import speech_recognition as sr

def takecommand():
    r = sr.Recognizer()
    with sr.Microphone() as source:
        print("Listening....")
        r.pause_threshold = 10
        audio = r.listen(source,0,2)
    try:
        print("Recognizing....")
        query = r.recognize_google(audio,language='en-in')
        print(f"You Said : {query}\n")
    except:
        return ""
    query = str(query)
    return query.lower()


while True:
    wakeUp = takecommand()

    if "wake up" in wakeUp or "up" in wakeUp:
        os.startfile('E:\\Jarvis\\JARVIS.9869\\JARVIS9869.py')
    elif "friday" in wakeUp:
        os.startfile('E:\\Jarvis\\JARVIS.9869\\FRIDAY9869.py')
    else:
        print("Sorry Say That Again...")

    