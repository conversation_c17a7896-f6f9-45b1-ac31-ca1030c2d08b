#!/usr/bin/env python3
"""
Enhanced JARVIS Installation Script
Automated setup and configuration for Enhanced JARVIS
"""

import os
import sys
import subprocess
import platform
from pathlib import Path
import json

def print_banner():
    """Print installation banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    Enhanced JARVIS                           ║
    ║              Intelligent AI Assistant                       ║
    ║                   Installation Script                       ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """Check if Python version is compatible"""
    print("🔍 Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        sys.exit(1)
    
    print(f"✅ Python {sys.version.split()[0]} - Compatible")

def check_system_requirements():
    """Check system requirements"""
    print("\n🔍 Checking system requirements...")
    
    system = platform.system()
    print(f"   Operating System: {system}")
    
    # Check for required system packages
    if system == "Linux":
        print("   Note: You may need to install additional packages:")
        print("   sudo apt-get install python3-dev portaudio19-dev")
    elif system == "Darwin":  # macOS
        print("   Note: You may need to install portaudio:")
        print("   brew install portaudio")
    elif system == "Windows":
        print("   Windows detected - most packages should work out of the box")
    
    print("✅ System check complete")

def install_dependencies():
    """Install Python dependencies"""
    print("\n📦 Installing Python dependencies...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ Error: requirements.txt not found")
        sys.exit(1)
    
    try:
        # Upgrade pip first
        print("   Upgrading pip...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # Install requirements
        print("   Installing packages...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ])
        
        print("✅ Dependencies installed successfully")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        print("   Try installing manually: pip install -r requirements.txt")
        return False
    
    return True

def download_spacy_model():
    """Download spaCy language model"""
    print("\n🧠 Downloading spaCy language model...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "spacy", "download", "en_core_web_sm"
        ])
        print("✅ spaCy model downloaded successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"⚠️  Warning: Could not download spaCy model: {e}")
        print("   You can download it manually later with:")
        print("   python -m spacy download en_core_web_sm")
        return False

def create_directories():
    """Create necessary directories"""
    print("\n📁 Creating directories...")
    
    directories = [
        "enhanced_jarvis/data",
        "enhanced_jarvis/logs",
        "enhanced_jarvis/config",
        "enhanced_jarvis/plugins"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"   Created: {directory}")
    
    print("✅ Directories created")

def create_default_config():
    """Create default configuration file"""
    print("\n⚙️  Creating default configuration...")
    
    config = {
        "ai_engine": {
            "context": {
                "max_history_length": 50,
                "cleanup_interval": 300
            },
            "memory": {
                "db_path": "enhanced_jarvis/data/memory.db",
                "max_memories_per_user": 10000
            },
            "intent": {
                "confidence_threshold": 0.3
            },
            "response": {
                "personality": "helpful",
                "verbosity": "moderate"
            },
            "decision": {
                "safety_checks": True
            }
        },
        "speech_recognition": {
            "sample_rate": 16000,
            "language": "en-US",
            "wake_words": ["jarvis", "hey jarvis"],
            "continuous_listening": True,
            "noise_threshold": 300
        },
        "text_to_speech": {
            "engine": "pyttsx3",
            "default_profile": "default",
            "voice_profiles": {
                "default": {
                    "voice_id": 0,
                    "rate": 150,
                    "volume": 0.9
                }
            }
        },
        "task_executor": {
            "max_concurrent_tasks": 5,
            "timeout": 30
        },
        "logging": {
            "level": "INFO",
            "file": "enhanced_jarvis/logs/jarvis.log"
        }
    }
    
    config_file = Path("enhanced_jarvis/config/default_config.json")
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"   Created: {config_file}")
    print("✅ Default configuration created")

def test_installation():
    """Test the installation"""
    print("\n🧪 Testing installation...")
    
    try:
        # Test imports
        print("   Testing core imports...")
        
        # Test speech recognition
        import speech_recognition as sr
        print("   ✅ Speech recognition")
        
        # Test TTS
        import pyttsx3
        print("   ✅ Text-to-speech")
        
        # Test other core libraries
        import numpy
        import sklearn
        print("   ✅ Machine learning libraries")
        
        # Test spaCy
        try:
            import spacy
            nlp = spacy.load("en_core_web_sm")
            print("   ✅ spaCy with language model")
        except OSError:
            print("   ⚠️  spaCy model not available (optional)")
        
        print("✅ Installation test passed")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def create_launcher_script():
    """Create launcher script"""
    print("\n🚀 Creating launcher script...")
    
    if platform.system() == "Windows":
        launcher_content = """@echo off
echo Starting Enhanced JARVIS...
python -m enhanced_jarvis.main %*
pause
"""
        launcher_file = "start_jarvis.bat"
    else:
        launcher_content = """#!/bin/bash
echo "Starting Enhanced JARVIS..."
python3 -m enhanced_jarvis.main "$@"
"""
        launcher_file = "start_jarvis.sh"
    
    with open(launcher_file, 'w') as f:
        f.write(launcher_content)
    
    if platform.system() != "Windows":
        os.chmod(launcher_file, 0o755)
    
    print(f"   Created: {launcher_file}")
    print("✅ Launcher script created")

def print_completion_message():
    """Print installation completion message"""
    message = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                 Installation Complete! 🎉                   ║
    ╚══════════════════════════════════════════════════════════════╝
    
    Enhanced JARVIS has been successfully installed!
    
    🚀 Quick Start:
    
    1. Voice Mode (Recommended):
       python -m enhanced_jarvis.main
    
    2. Text Mode (Testing):
       python -m enhanced_jarvis.main --text-mode
    
    3. With Custom Config:
       python -m enhanced_jarvis.main --config enhanced_jarvis/config/default_config.json
    
    📚 Documentation:
       See README.md for detailed usage instructions
    
    🔧 Configuration:
       Edit enhanced_jarvis/config/default_config.json to customize settings
    
    🎤 Voice Commands to Try:
       - "Hello Jarvis"
       - "What time is it?"
       - "Search for Python tutorials"
       - "Open Chrome"
       - "Remember to buy milk"
    
    ⚠️  Important Notes:
       - Make sure your microphone is working
       - Grant microphone permissions if prompted
       - Some features may require internet connection
    
    Enjoy your new AI assistant! 🤖
    """
    print(message)

def main():
    """Main installation function"""
    print_banner()
    
    try:
        # Check requirements
        check_python_version()
        check_system_requirements()
        
        # Install components
        if not install_dependencies():
            print("\n❌ Installation failed at dependency installation")
            sys.exit(1)
        
        download_spacy_model()  # Optional, continue even if fails
        
        # Setup
        create_directories()
        create_default_config()
        create_launcher_script()
        
        # Test
        if not test_installation():
            print("\n⚠️  Installation completed with warnings")
            print("   Some features may not work properly")
        
        print_completion_message()
        
    except KeyboardInterrupt:
        print("\n\n❌ Installation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Installation failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
