from datetime import datetime
from os import startfile
import os
from pyautogui import click
from keyboard import press
from keyboard import press_and_release
from keyboard import write
from time import sleep
from notifypy import Notify
import pyttsx3
import speech_recognition as sr
from geopy.distance import great_circle
from geopy.geocoders import Nominatim
import geocoder
import webbrowser as web

engine = pyttsx3.init('sapi5')
voices = engine.getProperty('voices')
engine.setProperty('voices',voices[0].id)

def Speak(audio):
    print(" ")
    print(f": {audio}")
    engine.say(audio)
    engine.runAndWait()
    print(" ")

def TakeCommand():

    r = sr.Recognizer()

    with sr.Microphone() as source:

        print(": Listening....")

        r.pause_threshold = 1

        audio = r.listen(source)


    try:

        print(": Recognizing...")

        query = r.recognize_google(audio,language='en-in')

        print(f": Your Command : {query}\n")

    except:
        return ""

    return query.lower()

def WhatsappMsg(name,message):
     
    startfile("C:\\Users\\<USER>\\AppData\\Local\\WhatsApp\\WhatsApp.exe")

    sleep(14)

    click(x=202, y=110)

    sleep(1)

    write(name)

    sleep(1)

    click(x=136, y=228)

    sleep(1)

    click(x=555, y=692)

    sleep(1)

    write(message)

    press('enter')

def WhatsappCall(name):
    startfile("C:\\Users\\<USER>\\AppData\\Local\\WhatsApp\\WhatsApp.exe")
    sleep(7)
    click(x=202, y=110)
    sleep(1)
    write(name)
    sleep(1)
    click(x=136, y=228)
    sleep(1)
    click(x=571, y=690)
    sleep(1)
    click(1208, y=59)

def WhatsappVideo(name):
    startfile("C:\\Users\\<USER>\\AppData\\Local\\WhatsApp\\WhatsApp.exe")
    sleep(7)
    click(x=202, y=110)
    sleep(1)
    write(name)
    sleep(1)
    click(x=136, y=228)
    sleep(1)
    click(x=571, y=690)
    sleep(1)
    click(x=1155, y=55)

def Notepad():

    Speak("Tell Me The Query .")
    Speak("I Am Ready To Write .")

    writes = TakeCommand()

    time = datetime.now().strftime("%H:%M")

    filename = str(time).replace(":","-") + "-note.txt"

    with open(filename,"w") as file:

        file.write(writes)

    path_1 = "C:\\Users\\<USER>\\Pictures\\JARVIS.9869\\JARVIS.9869\\" + str(filename)

    path_2 = "C:\\Users\\<USER>\\Pictures\\JARVIS.9869\\JARVIS.9869\\DataBase\\NotePad\\" + str(filename)

    os.rename(path_1,path_2)

    os.startfile(path_2)

def CloseNotepad():

    os.system("TASKKILL /F /im Notepad.exe")

def TimeTable():

    Speak("Checking....")

    from DataBase.TimeTable.TimeTable import Time

    value = Time()

    Noti = Notify()

    Noti.title = "TimeTable"

    Noti.message = str(value)

    Noti.send()

    Speak("AnyThing Else Sir ??")

