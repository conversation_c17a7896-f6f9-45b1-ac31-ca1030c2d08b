2025-07-11 12:29:06,138 - main - INFO - Initializing Enhanced JARVIS...
2025-07-11 12:29:06,138 - core.ai_engine - INFO - Initializing Enhanced JARVIS AI Engine...
2025-07-11 12:29:06,139 - core.memory_system - INFO - Initializing Memory System...
2025-07-11 12:29:06,189 - core.memory_system - INFO - Memory System initialized successfully
2025-07-11 12:29:06,189 - core.context_manager - INFO - Initializing Context Manager...
2025-07-11 12:29:06,189 - core.context_manager - INFO - Context Manager initialized successfully
2025-07-11 12:29:06,190 - core.intent_recognition - INFO - Initializing Intent Recognizer...
2025-07-11 12:29:06,190 - core.intent_recognition - INFO - spaCy not available, using basic processing
2025-07-11 12:29:06,191 - core.intent_recognition - INFO - Sklearn not available, using pattern-based recognition only
2025-07-11 12:29:06,191 - core.intent_recognition - INFO - Intent Recognizer initialized successfully
2025-07-11 12:29:06,191 - core.response_generator - INFO - Initializing Response Generator...
2025-07-11 12:29:06,191 - core.response_generator - INFO - Response Generator initialized successfully
2025-07-11 12:29:06,191 - core.decision_engine - INFO - Initializing Decision Engine...
2025-07-11 12:29:06,191 - core.decision_engine - INFO - Decision Engine initialized successfully
2025-07-11 12:29:06,191 - core.ai_engine - INFO - AI Engine initialized successfully
2025-07-11 12:29:06,192 - voice.speech_recognition - INFO - Initializing Enhanced Speech Recognizer...
2025-07-11 12:29:06,802 - voice.speech_recognition - INFO - Calibrating for ambient noise...
2025-07-11 12:29:08,804 - voice.speech_recognition - INFO - Noise threshold set to: 39.35599050749323
2025-07-11 12:29:08,855 - voice.speech_recognition - INFO - Speech Recognizer initialized successfully
2025-07-11 12:29:08,856 - voice.text_to_speech - INFO - Initializing Enhanced Text-to-Speech...
2025-07-11 12:29:08,891 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\Pictures\\JARVIS.9869\\JARVIS.9869\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-11 12:29:08,891 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\Pictures\JARVIS.9869\JARVIS.9869\.venv\Lib\site-packages\comtypes\gen'
2025-07-11 12:29:09,447 - voice.text_to_speech - INFO - Text-to-Speech initialized successfully
2025-07-11 12:29:09,447 - main - ERROR - Failed to initialize JARVIS: 'task_executor'
2025-07-11 12:29:09,448 - main - INFO - Shutting down Enhanced JARVIS...
2025-07-11 12:29:11,449 - voice.speech_recognition - INFO - Stopped listening
2025-07-11 12:29:11,450 - voice.speech_recognition - INFO - Speech Recognizer shutdown complete
2025-07-11 12:29:11,673 - voice.text_to_speech - INFO - Stopped speaking
2025-07-11 12:29:11,843 - voice.text_to_speech - INFO - Text-to-Speech shutdown complete
2025-07-11 12:29:11,843 - core.ai_engine - INFO - Shutting down AI Engine...
2025-07-11 12:29:11,844 - core.decision_engine - INFO - Decision Engine shutdown complete
2025-07-11 12:29:11,844 - core.response_generator - INFO - Response Generator shutdown complete
2025-07-11 12:29:11,844 - core.intent_recognition - INFO - Intent Recognizer shutdown complete
2025-07-11 12:29:11,844 - core.context_manager - INFO - Context Manager shutdown complete
2025-07-11 12:29:11,844 - core.memory_system - INFO - Memory System shutdown complete
2025-07-11 12:29:11,844 - core.ai_engine - INFO - AI Engine shutdown complete
2025-07-11 12:29:11,845 - main - INFO - Enhanced JARVIS shutdown complete
2025-07-11 12:30:00,702 - main - INFO - Initializing Enhanced JARVIS...
2025-07-11 12:30:00,703 - core.ai_engine - INFO - Initializing Enhanced JARVIS AI Engine...
2025-07-11 12:30:00,703 - core.memory_system - INFO - Initializing Memory System...
2025-07-11 12:30:00,704 - core.memory_system - INFO - Memory System initialized successfully
2025-07-11 12:30:00,705 - core.context_manager - INFO - Initializing Context Manager...
2025-07-11 12:30:00,705 - core.context_manager - INFO - Context Manager initialized successfully
2025-07-11 12:30:00,705 - core.intent_recognition - INFO - Initializing Intent Recognizer...
2025-07-11 12:30:00,706 - core.intent_recognition - INFO - spaCy not available, using basic processing
2025-07-11 12:30:00,706 - core.intent_recognition - INFO - Sklearn not available, using pattern-based recognition only
2025-07-11 12:30:00,707 - core.intent_recognition - INFO - Intent Recognizer initialized successfully
2025-07-11 12:30:00,707 - core.response_generator - INFO - Initializing Response Generator...
2025-07-11 12:30:00,707 - core.response_generator - INFO - Response Generator initialized successfully
2025-07-11 12:30:00,708 - core.decision_engine - INFO - Initializing Decision Engine...
2025-07-11 12:30:00,708 - core.decision_engine - INFO - Decision Engine initialized successfully
2025-07-11 12:30:00,708 - core.ai_engine - INFO - AI Engine initialized successfully
2025-07-11 12:30:00,708 - voice.speech_recognition - INFO - Initializing Enhanced Speech Recognizer...
2025-07-11 12:30:01,222 - voice.speech_recognition - INFO - Calibrating for ambient noise...
2025-07-11 12:30:03,222 - voice.speech_recognition - INFO - Noise threshold set to: 35.75690905980765
2025-07-11 12:30:03,269 - voice.speech_recognition - INFO - Speech Recognizer initialized successfully
2025-07-11 12:30:03,269 - voice.text_to_speech - INFO - Initializing Enhanced Text-to-Speech...
2025-07-11 12:30:03,300 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\Pictures\\JARVIS.9869\\JARVIS.9869\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-11 12:30:03,300 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\Pictures\JARVIS.9869\JARVIS.9869\.venv\Lib\site-packages\comtypes\gen'
2025-07-11 12:30:03,744 - voice.text_to_speech - INFO - Text-to-Speech initialized successfully
2025-07-11 12:30:03,744 - automation.task_executor - INFO - Initializing Task Executor...
2025-07-11 12:30:03,746 - automation.task_executor - WARNING - Subprocess verification failed: [WinError 2] The system cannot find the file specified
2025-07-11 12:30:03,746 - automation.task_executor - INFO - Task Executor initialized successfully
2025-07-11 12:30:03,746 - main - INFO - Enhanced JARVIS initialized successfully!
2025-07-11 12:30:34,089 - core.ai_engine - INFO - Processed input successfully: greeting
2025-07-11 12:30:35,106 - core.ai_engine - INFO - Processed input successfully: question_time
2025-07-11 12:30:36,125 - core.ai_engine - INFO - Processed input successfully: question_date
2025-07-11 12:30:37,153 - core.ai_engine - INFO - Processed input successfully: remember_something
2025-07-11 12:30:38,191 - core.ai_engine - INFO - Processed input successfully: remember_something
2025-07-11 12:30:39,211 - core.ai_engine - INFO - Processed input successfully: search_web
2025-07-11 12:30:40,317 - core.ai_engine - INFO - Processed input successfully: open_application
2025-07-11 12:30:41,350 - core.ai_engine - INFO - Processed input successfully: how_are_you
2025-07-11 12:30:42,369 - core.ai_engine - INFO - Processed input successfully: help
2025-07-11 12:30:43,393 - core.ai_engine - INFO - Processed input successfully: how_are_you
2025-07-11 12:30:44,396 - main - INFO - Shutting down Enhanced JARVIS...
2025-07-11 12:30:46,397 - automation.task_executor - INFO - Task Executor shutdown complete
2025-07-11 12:30:46,398 - voice.speech_recognition - INFO - Stopped listening
2025-07-11 12:30:46,398 - voice.speech_recognition - INFO - Speech Recognizer shutdown complete
2025-07-11 12:30:46,631 - voice.text_to_speech - INFO - Stopped speaking
2025-07-11 12:30:46,767 - voice.text_to_speech - INFO - Text-to-Speech shutdown complete
2025-07-11 12:30:46,768 - core.ai_engine - INFO - Shutting down AI Engine...
2025-07-11 12:30:46,768 - core.decision_engine - INFO - Decision Engine shutdown complete
2025-07-11 12:30:46,768 - core.response_generator - INFO - Response Generator shutdown complete
2025-07-11 12:30:46,769 - core.intent_recognition - INFO - Intent Recognizer shutdown complete
2025-07-11 12:30:46,769 - core.context_manager - INFO - Context Manager shutdown complete
2025-07-11 12:30:46,770 - core.memory_system - INFO - Memory System shutdown complete
2025-07-11 12:30:46,770 - core.ai_engine - INFO - AI Engine shutdown complete
2025-07-11 12:30:46,770 - main - INFO - Enhanced JARVIS shutdown complete
2025-07-11 12:32:33,622 - __main__ - INFO - Initializing Enhanced JARVIS...
2025-07-11 12:32:33,623 - core.ai_engine - INFO - Initializing Enhanced JARVIS AI Engine...
2025-07-11 12:32:33,623 - core.memory_system - INFO - Initializing Memory System...
2025-07-11 12:32:33,659 - core.memory_system - INFO - Memory System initialized successfully
2025-07-11 12:32:33,660 - core.context_manager - INFO - Initializing Context Manager...
2025-07-11 12:32:33,660 - core.context_manager - INFO - Context Manager initialized successfully
2025-07-11 12:32:33,660 - core.intent_recognition - INFO - Initializing Intent Recognizer...
2025-07-11 12:32:33,660 - core.intent_recognition - INFO - spaCy not available, using basic processing
2025-07-11 12:32:33,660 - core.intent_recognition - INFO - Sklearn not available, using pattern-based recognition only
2025-07-11 12:32:33,660 - core.intent_recognition - INFO - Intent Recognizer initialized successfully
2025-07-11 12:32:33,660 - core.response_generator - INFO - Initializing Response Generator...
2025-07-11 12:32:33,661 - core.response_generator - INFO - Response Generator initialized successfully
2025-07-11 12:32:33,661 - core.decision_engine - INFO - Initializing Decision Engine...
2025-07-11 12:32:33,661 - core.decision_engine - INFO - Decision Engine initialized successfully
2025-07-11 12:32:33,661 - core.ai_engine - INFO - AI Engine initialized successfully
2025-07-11 12:32:33,661 - voice.speech_recognition - INFO - Initializing Enhanced Speech Recognizer...
2025-07-11 12:32:34,099 - voice.speech_recognition - INFO - Calibrating for ambient noise...
2025-07-11 12:32:36,099 - voice.speech_recognition - INFO - Noise threshold set to: 35.33981456267512
2025-07-11 12:32:36,146 - voice.speech_recognition - INFO - Speech Recognizer initialized successfully
2025-07-11 12:32:36,147 - voice.text_to_speech - INFO - Initializing Enhanced Text-to-Speech...
2025-07-11 12:32:36,185 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\Pictures\\JARVIS.9869\\JARVIS.9869\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-11 12:32:36,185 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\Pictures\JARVIS.9869\JARVIS.9869\.venv\Lib\site-packages\comtypes\gen'
2025-07-11 12:32:36,663 - voice.text_to_speech - INFO - Text-to-Speech initialized successfully
2025-07-11 12:32:36,663 - automation.task_executor - INFO - Initializing Task Executor...
2025-07-11 12:32:36,665 - automation.task_executor - WARNING - Subprocess verification failed: [WinError 2] The system cannot find the file specified
2025-07-11 12:32:36,666 - automation.task_executor - INFO - Task Executor initialized successfully
2025-07-11 12:32:36,666 - __main__ - INFO - Enhanced JARVIS initialized successfully!
2025-07-11 12:33:07,902 - core.ai_engine - INFO - Processed input successfully: greeting
2025-07-11 12:33:08,731 - core.ai_engine - INFO - Processed input successfully: greeting
2025-07-11 12:33:28,461 - core.ai_engine - INFO - Processed input successfully: remember_something
2025-07-11 12:34:34,129 - __main__ - INFO - Initializing Enhanced JARVIS...
2025-07-11 12:34:34,130 - core.ai_engine - INFO - Initializing Enhanced JARVIS AI Engine...
2025-07-11 12:34:34,130 - core.memory_system - INFO - Initializing Memory System...
2025-07-11 12:34:34,132 - core.memory_system - INFO - Memory System initialized successfully
2025-07-11 12:34:34,132 - core.context_manager - INFO - Initializing Context Manager...
2025-07-11 12:34:34,132 - core.context_manager - INFO - Context Manager initialized successfully
2025-07-11 12:34:34,132 - core.intent_recognition - INFO - Initializing Intent Recognizer...
2025-07-11 12:34:34,132 - core.intent_recognition - INFO - spaCy not available, using basic processing
2025-07-11 12:34:34,133 - core.intent_recognition - INFO - Sklearn not available, using pattern-based recognition only
2025-07-11 12:34:34,133 - core.intent_recognition - INFO - Intent Recognizer initialized successfully
2025-07-11 12:34:34,133 - core.response_generator - INFO - Initializing Response Generator...
2025-07-11 12:34:34,133 - core.response_generator - INFO - Response Generator initialized successfully
2025-07-11 12:34:34,133 - core.decision_engine - INFO - Initializing Decision Engine...
2025-07-11 12:34:34,134 - core.decision_engine - INFO - Decision Engine initialized successfully
2025-07-11 12:34:34,134 - core.ai_engine - INFO - AI Engine initialized successfully
2025-07-11 12:34:34,134 - voice.speech_recognition - INFO - Initializing Enhanced Speech Recognizer...
2025-07-11 12:34:34,653 - voice.speech_recognition - INFO - Calibrating for ambient noise...
2025-07-11 12:34:36,652 - voice.speech_recognition - INFO - Noise threshold set to: 28.467617090985495
2025-07-11 12:34:36,692 - voice.speech_recognition - INFO - Speech Recognizer initialized successfully
2025-07-11 12:34:36,693 - voice.text_to_speech - INFO - Initializing Enhanced Text-to-Speech...
2025-07-11 12:34:36,724 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\Pictures\\JARVIS.9869\\JARVIS.9869\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-11 12:34:36,724 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\Pictures\JARVIS.9869\JARVIS.9869\.venv\Lib\site-packages\comtypes\gen'
2025-07-11 12:34:37,195 - voice.text_to_speech - INFO - Text-to-Speech initialized successfully
2025-07-11 12:34:37,195 - automation.task_executor - INFO - Initializing Task Executor...
2025-07-11 12:34:37,197 - automation.task_executor - WARNING - Subprocess verification failed: [WinError 2] The system cannot find the file specified
2025-07-11 12:34:37,197 - automation.task_executor - INFO - Task Executor initialized successfully
2025-07-11 12:34:37,197 - __main__ - INFO - Enhanced JARVIS initialized successfully!
2025-07-11 12:34:37,197 - __main__ - INFO - Starting Enhanced JARVIS main loop...
2025-07-11 12:34:37,198 - voice.speech_recognition - INFO - Started continuous listening
